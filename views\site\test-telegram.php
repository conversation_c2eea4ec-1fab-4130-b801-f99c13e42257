<?php

/* @var $this yii\web\View */
/* @var $result array */

use yii\helpers\Html;
use yii\helpers\VarDumper;

$this->title = 'Test Telegram API';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="site-test-telegram">
    <h1><?= Html::encode($this->title) ?></h1>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h3>🤖 Тест Telegram Bot API</h3>
                </div>
                <div class="card-body">
                    <?php if ($result['bot_info']): ?>
                        <div class="alert alert-success">
                            ✅ Соединение с Telegram Bot API установлено успешно!
                        </div>
                        
                        <h4>Информация о боте:</h4>
                        <table class="table table-striped">
                            <tr>
                                <td><strong>ID:</strong></td>
                                <td><?= Html::encode($result['bot_info']['id']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Username:</strong></td>
                                <td>@<?= Html::encode($result['bot_info']['username']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>First Name:</strong></td>
                                <td><?= Html::encode($result['bot_info']['first_name']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Is Bot:</strong></td>
                                <td><?= $result['bot_info']['is_bot'] ? 'Да' : 'Нет' ?></td>
                            </tr>
                        </table>
                        
                        <h4 class="mt-3">Информация о Webhook:</h4>
                        <?php if ($result['webhook_info']): ?>
                            <table class="table table-striped">
                                <tr>
                                    <td><strong>URL:</strong></td>
                                    <td>
                                        <?php if (!empty($result['webhook_info']['url'])): ?>
                                            <span class="text-success">✅ Установлен</span><br>
                                            <small class="text-muted"><?= Html::encode($result['webhook_info']['url']) ?></small>
                                        <?php else: ?>
                                            <span class="text-warning">⚠️ Не установлен</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Pending Updates:</strong></td>
                                    <td><?= $result['webhook_info']['pending_update_count'] ?></td>
                                </tr>
                            </table>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                ⚠️ Не удалось получить информацию о webhook.
                            </div>
                        <?php endif; ?>
                        
                        <details class="mt-3">
                            <summary>Полные данные</summary>
                            <pre class="bg-light p-3 rounded mt-2"><?= VarDumper::dumpAsString($result, 10, true) ?></pre>
                        </details>
                    <?php else: ?>
                        <div class="alert alert-danger">
                            ❌ Ошибка соединения с Telegram Bot API. Проверьте токен.
                        </div>
                    <?php endif; ?>
                    
                    <div class="mt-3">
                        <?= Html::a('🏠 На главную', ['/'], ['class' => 'btn btn-primary']) ?>
                        <?= Html::a('🔄 Повторить тест', ['/site/test-telegram'], ['class' => 'btn btn-secondary']) ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>