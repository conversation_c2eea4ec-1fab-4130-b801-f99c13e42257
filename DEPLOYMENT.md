# Инструкция по развертыванию Telegram-бота

## Пошаговое руководство по установке и настройке

### 1. Подготовка окружения

#### Требования:
- PHP 7.4 или выше
- Composer
- <PERSON><PERSON><PERSON>-сервер (Apache/Nginx)
- Доступ к интернету для API вызовов

#### Для OSPanel (Windows):
1. Убедитесь, что включен модуль `mod_rewrite` в Apache
2. PHP должен иметь расширения: `pdo`, `pdo_sqlite`, `curl`, `json`, `mbstring`

### 2. Установка зависимостей

Откройте командную строку в корневой папке проекта и выполните:

```bash
composer install
```

Если composer не установлен, скачайте его с https://getcomposer.org/

### 3. Создание Telegram бота

1. Найдите в Telegram бота @BotFather
2. Отправьте команду `/newbot`
3. Следуйте инструкциям для создания бота
4. Сохраните полученный токен (например: `123456789:ABCdefGhIJKlmNoPQRsTUVwxyZ`)

### 4. Получение Groq API ключа

1. Зарегистрируйтесь на https://console.groq.com/
2. Создайте новый API ключ
3. Сохраните ключ (например: `gsk_...`)

### 5. Настройка параметров

Отредактируйте файл `config/params.php`:

```php
<?php
return [
    'telegramBotToken' => '123456789:ABCdefGhIJKlmNoPQRsTUVwxyZ', // Ваш токен бота
    'groqApiKey' => 'gsk_ваш_ключ_groq_api_здесь',
    'webhookUrl' => 'https://yourdomain.com/webhook/123456789:ABCdefGhIJKlmNoPQRsTUVwxyZ',
    'adminChatId' => '123456789', // Ваш Telegram ID (необязательно)
];
```

**Важно**: Замените `yourdomain.com` на ваш реальный домен!

### 6. Создание базы данных

Выполните миграции для создания таблиц:

```bash
php yii migrate
```

Ответьте `yes` на вопрос о применении миграций.

### 7. Настройка веб-сервера

#### Для Apache:
Убедитесь, что корневая директория сайта указывает на папку `web/`.

Пример конфигурации виртуального хоста:
```apache
<VirtualHost *:80>
    DocumentRoot "D:/OSPanel/domains/groq bot/web"
    ServerName groq-bot.local
    
    <Directory "D:/OSPanel/domains/groq bot/web">
        AllowOverride All
        Require all granted
    </Directory>
</VirtualHost>
```

#### Для Nginx:
```nginx
server {
    listen 80;
    server_name groq-bot.local;
    root /path/to/groq-bot/web;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

### 8. Установка webhook

#### Через веб-интерфейс:
1. Откройте ваш сайт в браузере
2. Нажмите кнопку "Установить Webhook"
3. Проверьте статус webhook

#### Через консоль:
```bash
php yii bot/set-webhook
```

### 9. Тестирование

#### Проверка компонентов:
```bash
php yii bot/test
```

#### Проверка через веб-интерфейс:
1. Перейдите на главную страницу
2. Нажмите "Тест Telegram API"
3. Нажмите "Тест Groq API"

### 10. Использование бота

1. Найдите вашего бота в Telegram по username
2. Отправьте команду `/start`
3. Начните общение!

## Консольные команды

### Управление webhook:
```bash
php yii bot/set-webhook          # Установить webhook
php yii bot/delete-webhook       # Удалить webhook  
php yii bot/webhook-info         # Информация о webhook
```

### Тестирование:
```bash
php yii bot/test                 # Тест всех компонентов
```

### Обслуживание:
```bash
php yii bot/clean-messages 30    # Очистить сообщения старше 30 дней
```

### Миграции:
```bash
php yii migrate                  # Применить новые миграции
php yii migrate/down             # Откатить последнюю миграцию
```

## Возможные проблемы и решения

### 1. Ошибка "Class 'Composer\Autoload\ClassLoader' not found"
**Решение**: Выполните `composer install`

### 2. Ошибка подключения к базе данных
**Решение**: 
- Проверьте права доступа к папке `runtime/`
- Убедитесь, что PHP имеет расширение `pdo_sqlite`

### 3. Webhook не устанавливается
**Решение**:
- Проверьте, что ваш сайт доступен по HTTPS (для продакшена)
- Убедитесь, что URL в `webhookUrl` корректный
- Проверьте токен бота

### 4. Groq API возвращает ошибки
**Решение**:
- Проверьте правильность API ключа
- Убедитесь, что у вас есть кредиты на аккаунте Groq
- Проверьте интернет-соединение

### 5. Бот не отвечает на сообщения
**Решение**:
- Проверьте логи в `runtime/logs/telegram.log`
- Убедитесь, что webhook установлен корректно
- Проверьте, что веб-сервер обрабатывает POST запросы

## Настройка для продакшена

### 1. Изменение окружения
В `web/index.php` и `yii` измените:
```php
defined('YII_DEBUG') or define('YII_DEBUG', false);
defined('YII_ENV') or define('YII_ENV', 'prod');
```

### 2. Настройка базы данных
Для продакшена рекомендуется использовать MySQL или PostgreSQL.
Измените `config/db.php`:
```php
return [
    'class' => 'yii\db\Connection',
    'dsn' => 'mysql:host=localhost;dbname=telegram_bot',
    'username' => 'your_username',
    'password' => 'your_password',
    'charset' => 'utf8',
];
```

### 3. HTTPS
Telegram требует HTTPS для webhook в продакшене. Настройте SSL сертификат.

### 4. Безопасность
- Ограничьте доступ к файлам конфигурации
- Используйте переменные окружения для чувствительных данных
- Настройте регулярные бэкапы базы данных

## Мониторинг

### Логи
- `runtime/logs/app.log` - общие логи приложения
- `runtime/logs/telegram.log` - логи Telegram API

### Статистика
Веб-интерфейс предоставляет информацию о:
- Статусе бота и webhook
- Результатах тестирования API
- Общей статистике использования

## Обновление

1. Сделайте бэкап базы данных
2. Обновите код
3. Выполните `composer update`
4. Примените новые миграции: `php yii migrate`
5. Очистите кэш: `rm -rf runtime/cache/*`

## Поддержка

При возникновении проблем:
1. Проверьте логи в `runtime/logs/`
2. Убедитесь, что все зависимости установлены
3. Проверьте конфигурацию веб-сервера
4. Используйте команду `php yii bot/test` для диагностики