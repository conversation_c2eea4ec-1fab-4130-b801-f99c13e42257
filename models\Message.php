<?php

namespace app\models;

use Yii;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "messages".
 *
 * @property int $id
 * @property int $conversation_id
 * @property int $message_id
 * @property string $role
 * @property string $content
 * @property int $tokens_used
 * @property string $created_at
 *
 * @property Conversation $conversation
 */
class Message extends ActiveRecord
{
    const ROLE_USER = 'user';
    const ROLE_ASSISTANT = 'assistant';
    const ROLE_SYSTEM = 'system';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'messages';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['conversation_id', 'message_id', 'role', 'content'], 'required'],
            [['conversation_id', 'message_id', 'tokens_used'], 'integer'],
            [['content'], 'string'],
            [['created_at'], 'safe'],
            [['role'], 'string', 'max' => 20],
            [['role'], 'in', 'range' => [self::ROLE_USER, self::ROLE_ASSISTANT, self::ROLE_SYSTEM]],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'conversation_id' => 'Conversation ID',
            'message_id' => 'Message ID',
            'role' => 'Role',
            'content' => 'Content',
            'tokens_used' => 'Tokens Used',
            'created_at' => 'Created At',
        ];
    }

    /**
     * Gets query for the associated conversation.
     *
     * @return \yii\db\ActiveQuery
     */
    public function getConversation()
    {
        return $this->hasOne(Conversation::class, ['id' => 'conversation_id']);
    }

    /**
     * Creates a new message
     *
     * @param int $conversationId
     * @param string $role
     * @param string $content
     * @param int|null $messageId
     * @param int $tokensUsed
     * @return static
     */
    public static function create($conversationId, $role, $content, $messageId = null, $tokensUsed = 0)
    {
        $message = new static();
        $message->conversation_id = $conversationId;
        $message->message_id = $messageId ?: 0;
        $message->role = $role;
        $message->content = $content;
        $message->tokens_used = $tokensUsed;
        $message->created_at = date('Y-m-d H:i:s');
        $message->save();
        
        return $message;
    }

    /**
     * Gets messages for conversation context
     *
     * @param int $conversationId
     * @param int $limit
     * @return array
     */
    public static function getContextMessages($conversationId, $limit = 10)
    {
        $messages = static::find()
            ->where(['conversation_id' => $conversationId])
            ->orderBy(['created_at' => SORT_DESC])
            ->limit($limit)
            ->all();
        
        $context = [];
        foreach (array_reverse($messages) as $message) {
            $context[] = [
                'role' => $message->role,
                'content' => $message->content
            ];
        }
        
        return $context;
    }

    /**
     * Cleans old messages to maintain context size
     *
     * @param int $conversationId
     * @param int $keepCount
     */
    public static function cleanOldMessages($conversationId, $keepCount = 20)
    {
        $totalMessages = static::find()
            ->where(['conversation_id' => $conversationId])
            ->count();
        
        if ($totalMessages > $keepCount) {
            $messagesToDelete = $totalMessages - $keepCount;
            
            $oldMessages = static::find()
                ->where(['conversation_id' => $conversationId])
                ->orderBy(['created_at' => SORT_ASC])
                ->limit($messagesToDelete)
                ->all();
            
            foreach ($oldMessages as $message) {
                $message->delete();
            }
        }
    }
}