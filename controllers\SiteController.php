<?php

namespace app\controllers;

use app\models\User;
use Yii;
use yii\web\Controller;

/**
 * Site controller
 */
class SiteController extends Controller
{
    /**
     * {@inheritdoc}
     */
    public function actions()
    {
        return [
            'error' => [
                'class' => 'yii\web\ErrorAction',
            ],
        ];
    }
    /**
     * Shows user memory
     *
     * @return string
     */
    public function actionMemory()
    {
        $users = User::find()
            ->with(['memories'])
            ->orderBy(['created_at' => SORT_DESC])
            ->limit(10)
            ->all();

        return $this->render('memory', ['users' => $users]);
    }

    /**
     * Displays homepage.
     *
     * @return string
     */
    public function actionIndex()
    {
        $telegramBot = Yii::$app->telegramBot;
        $botInfo = $telegramBot->getMe();
        $webhookInfo = $telegramBot->getWebhookInfo();

        return $this->render('index', [
            'botInfo' => $botInfo,
            'webhookInfo' => $webhookInfo,
        ]);
    }

    /**
     * Test Groq API connection
     *
     * @return string
     */
    public function actionTestGroq()
    {
        $groqApi = Yii::$app->groqApi;
        
        $testMessage = 'Привет! Как дела?';
        $response = $groqApi->generateResponse($testMessage);
        
        $result = [
            'request' => $testMessage,
            'response' => $response,
            'extracted_text' => $groqApi->extractResponseText($response),
            'token_usage' => $groqApi->getTokenUsage($response),
        ];

        return $this->render('test-groq', ['result' => $result]);
    }

    /**
     * Test Telegram Bot API connection
     *
     * @return string
     */
    public function actionTestTelegram()
    {
        $telegramBot = Yii::$app->telegramBot;
        
        $botInfo = $telegramBot->getMe();
        $webhookInfo = $telegramBot->getWebhookInfo();
        
        $result = [
            'bot_info' => $botInfo,
            'webhook_info' => $webhookInfo,
        ];

        return $this->render('test-telegram', ['result' => $result]);
    }
}