<?php

namespace app\models;

use Yii;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "conversations".
 *
 * @property int $id
 * @property int $user_id
 * @property int $chat_id
 * @property string|null $context
 * @property string $created_at
 * @property string $updated_at
 *
 * @property User $user
 * @property Message[] $messages
 */
class Conversation extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'conversations';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['user_id', 'chat_id'], 'required'],
            [['user_id', 'chat_id'], 'integer'],
            [['context'], 'string'],
            [['created_at', 'updated_at'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => 'User ID',
            'chat_id' => 'Chat ID',
            'context' => 'Context',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for the associated user.
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(User::class, ['id' => 'user_id']);
    }

    /**
     * Gets query for the associated messages.
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMessages()
    {
        return $this->hasMany(Message::class, ['conversation_id' => 'id']);
    }

    /**
     * Finds conversation by user and chat
     *
     * @param int $userId
     * @param int $chatId
     * @return static|null
     */
    public static function findByUserAndChat($userId, $chatId)
    {
        return static::findOne(['user_id' => $userId, 'chat_id' => $chatId]);
    }

    /**
     * Creates or gets conversation for user and chat
     *
     * @param int $userId
     * @param int $chatId
     * @return static
     */
    public static function getOrCreate($userId, $chatId)
    {
        $conversation = static::findByUserAndChat($userId, $chatId);
        
        if (!$conversation) {
            $conversation = new static();
            $conversation->user_id = $userId;
            $conversation->chat_id = $chatId;
            $conversation->created_at = date('Y-m-d H:i:s');
            $conversation->updated_at = date('Y-m-d H:i:s');
            $conversation->save();
        }
        
        return $conversation;
    }

    /**
     * Gets recent messages for context
     *
     * @param int $limit
     * @return Message[]
     */
    public function getRecentMessages($limit = 10)
    {
        return $this->getMessages()
            ->orderBy(['created_at' => SORT_DESC])
            ->limit($limit)
            ->all();
    }

    /**
     * Adds message to conversation
     *
     * @param string $role
     * @param string $content
     * @param int|null $messageId
     * @param int $tokensUsed
     * @return Message
     */
    public function addMessage($role, $content, $messageId = null, $tokensUsed = 0)
    {
        $message = new Message();
        $message->conversation_id = $this->id;
        $message->message_id = $messageId ?: 0;
        $message->role = $role;
        $message->content = $content;
        $message->tokens_used = $tokensUsed;
        $message->created_at = date('Y-m-d H:i:s');
        $message->save();
        
        // Update conversation timestamp
        $this->updated_at = date('Y-m-d H:i:s');
        $this->save(false);
        
        return $message;
    }

    /**
     * Clears conversation history
     */
    public function clearHistory()
    {
        Message::deleteAll(['conversation_id' => $this->id]);
        $this->updated_at = date('Y-m-d H:i:s');
        $this->save(false);
    }

    /**
     * Gets conversation context for GPT
     *
     * @param int $maxMessages
     * @return array
     */
    public function getContextForGPT($maxMessages = 10)
    {
        $messages = $this->getMessages()
            ->orderBy(['created_at' => SORT_ASC])
            ->limit($maxMessages)
            ->all();
        
        $context = [];
        foreach ($messages as $message) {
            $context[] = [
                'role' => $message->role,
                'content' => $message->content
            ];
        }
        
        return $context;
    }
}