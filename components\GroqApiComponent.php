<?php

namespace app\components;

use Yii;
use yii\base\Component;
use Guz<PERSON>Http\Client;
use GuzzleHttp\Exception\RequestException;

/**
 * Groq API Component for GPT integration
 */
class GroqApiComponent extends Component
{
    public $apiKey;
    public $baseUrl = 'https://api.groq.com/openai/v1';
    public $model = 'llama-3.1-8b-instant'; // Проверенная рабочая модель
    public $maxTokens = 1024;
    public $temperature = 0.7;
    public $topP = 1;
    public $frequencyPenalty = 0;
    public $presencePenalty = 0;

    private $client;

    /**
     * {@inheritdoc}
     */
    public function init()
    {
        parent::init();

        if (!$this->apiKey) {
            throw new \yii\base\InvalidConfigException('Groq API key is required');
        }

        $this->client = new Client([
            'base_uri' => 'https://api.groq.com/',
            'timeout' => 30,
            'verify' => false, // Отключаем SSL проверку для разработки
            'headers' => [
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ],
        ]);
    }

    /**
     * Sends chat completion request to Groq API
     *
     * @param array $messages Array of messages in OpenAI format
     * @param array $options Additional options
     * @return array|null Response from API or null on error
     */
    public function chatCompletion($messages, $options = [])
    {
        $startTime = microtime(true);
        
        try {
            $data = [
                'model' => $options['model'] ?? $this->model,
                'messages' => $messages,
                'max_completion_tokens' => $options['max_completion_tokens'] ?? $options['max_tokens'] ?? $this->maxTokens,
                'temperature' => $options['temperature'] ?? $this->temperature,
                'top_p' => $options['top_p'] ?? $this->topP,
                'frequency_penalty' => $options['frequency_penalty'] ?? $this->frequencyPenalty,
                'presence_penalty' => $options['presence_penalty'] ?? $this->presencePenalty,
                'stream' => $options['stream'] ?? false,
                'stop' => $options['stop'] ?? null,
            ];

            // Детальное логирование запроса
            $this->logToFile("=== GROQ API REQUEST ===");
            $this->logToFile("Model: " . $data['model']);
            $this->logToFile("Messages count: " . count($messages));
            $this->logToFile("Max tokens: " . $data['max_completion_tokens']);
            $this->logToFile("Temperature: " . $data['temperature']);
            $this->logToFile("API Key: " . (empty($this->apiKey) ? 'NOT SET' : substr($this->apiKey, 0, 10) . '...'));
            $this->logToFile("Base URL: " . $this->baseUrl);
            $this->logToFile("Messages: " . json_encode($messages, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));

            Yii::info('Groq API Request: ' . json_encode($data), 'groq');

            $response = $this->client->post('openai/v1/chat/completions', [
                'json' => $data,
            ]);

            $responseBody = $response->getBody()->getContents();
            $responseData = json_decode($responseBody, true);
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);

            // Детальное логирование ответа
            $this->logToFile("=== GROQ API RESPONSE ===");
            $this->logToFile("HTTP Status: " . $response->getStatusCode());
            $this->logToFile("Response Time: {$responseTime}ms");
            $this->logToFile("Response Body: " . $responseBody);

            if (!$responseData) {
                $this->logToFile("ERROR: Failed to decode JSON response");
                $this->logToFile("Raw response: " . $responseBody);
                Yii::error('Groq API: Failed to decode JSON response: ' . $responseBody, 'groq');
                return null;
            }

            if (isset($responseData['error'])) {
                $this->logToFile("ERROR: API returned error");
                $this->logToFile("Error: " . json_encode($responseData['error'], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
                Yii::error('Groq API Error: ' . json_encode($responseData['error']), 'groq');
                return null;
            }

            if (!isset($responseData['choices']) || empty($responseData['choices'])) {
                $this->logToFile("ERROR: No choices in response");
                Yii::error('Groq API: No choices in response', 'groq');
                return null;
            }

            $this->logToFile("SUCCESS: Response received successfully");
            $this->logToFile("Choices count: " . count($responseData['choices']));
            
            if (isset($responseData['usage'])) {
                $usage = $responseData['usage'];
                $this->logToFile("Token usage: " . json_encode($usage));
            }

            $this->logToFile(""); // Empty line for separation

            Yii::info('Groq API Response: ' . json_encode($responseData), 'groq');

            return $responseData;
        } catch (RequestException $e) {
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);
            $errorMessage = 'Groq API Error: ' . $e->getMessage();
            $errorBody = '';

            if ($e->hasResponse()) {
                $errorBody = $e->getResponse()->getBody()->getContents();
                $errorMessage .= ' Response: ' . $errorBody;
            }

            $this->logToFile("=== GROQ API REQUEST EXCEPTION ===");
            $this->logToFile("Error: " . $e->getMessage());
            $this->logToFile("Response Time: {$responseTime}ms");
            if ($errorBody) {
                $this->logToFile("Error Response: " . $errorBody);
            }
            if ($e->hasResponse()) {
                $this->logToFile("HTTP Status: " . $e->getResponse()->getStatusCode());
            }
            $this->logToFile(""); // Empty line for separation

            Yii::error($errorMessage, 'groq');
            return null;
        } catch (\Exception $e) {
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);
            $errorMessage = 'Groq API Exception: ' . $e->getMessage();
            
            $this->logToFile("=== GROQ API GENERAL EXCEPTION ===");
            $this->logToFile("Exception: " . $e->getMessage());
            $this->logToFile("File: " . $e->getFile() . ":" . $e->getLine());
            $this->logToFile("Response Time: {$responseTime}ms");
            $this->logToFile(""); // Empty line for separation

            Yii::error($errorMessage, 'groq');
            return null;
        }
    }

    /**
     * Logs message to bot-specific log file
     *
     * @param string $message
     */
    private function logToFile($message)
    {
        $logFile = Yii::getAlias('@app/logs/bot.log');
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] GROQ: $message" . PHP_EOL;
        
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }

    /**
     * Generates response for user message with context
     *
     * @param string $userMessage
     * @param array $context Previous messages
     * @param array $options Additional options
     * @return array|null
     */
    public function generateResponse($userMessage, $context = [], $options = [])
    {
        // Prepare system message
        $systemMessage = [
            'role' => 'system',
            'content' => $options['system_prompt'] ?? 'Ты полезный AI-ассистент. Отвечай на русском языке, будь дружелюбным и полезным.'
        ];

        // Prepare messages array
        $messages = [$systemMessage];

        // Add context messages
        if (!empty($context)) {
            $messages = array_merge($messages, $context);
        }

        // Add current user message
        $messages[] = [
            'role' => 'user',
            'content' => $userMessage
        ];

        // Limit context size to prevent token overflow
        if (count($messages) > 20) {
            $messages = array_slice($messages, -19); // Keep system message + last 19 messages
            array_unshift($messages, $systemMessage); // Ensure system message is first
        }

        return $this->chatCompletion($messages, $options);
    }

    /**
     * Gets available models
     *
     * @return array|null
     */
    public function getModels()
    {
        try {
            $response = $this->client->get('openai/v1/models');
            return json_decode($response->getBody()->getContents(), true);
        } catch (RequestException $e) {
            Yii::error('Groq API Models Error: ' . $e->getMessage(), 'groq');
            return null;
        }
    }

    /**
     * Extracts response text from API response
     *
     * @param array $response
     * @return string|null
     */
    public function extractResponseText($response)
    {
        if (!$response || !isset($response['choices']) || empty($response['choices'])) {
            return null;
        }

        return $response['choices'][0]['message']['content'] ?? null;
    }

    /**
     * Gets token usage from response
     *
     * @param array $response
     * @return array
     */
    public function getTokenUsage($response)
    {
        return $response['usage'] ?? [
            'prompt_tokens' => 0,
            'completion_tokens' => 0,
            'total_tokens' => 0,
        ];
    }

    /**
     * Stream chat completion (for real-time responses) - как в Python примере
     *
     * @param array $messages
     * @param callable $callback Function to handle each chunk
     * @param array $options
     * @return bool Success status
     */
    public function streamChatCompletion($messages, $callback, $options = [])
    {
        try {
            $data = [
                'model' => $options['model'] ?? $this->model,
                'messages' => $messages,
                'max_completion_tokens' => $options['max_completion_tokens'] ?? $options['max_tokens'] ?? $this->maxTokens,
                'temperature' => $options['temperature'] ?? $this->temperature,
                'top_p' => $options['top_p'] ?? $this->topP,
                'stream' => true,
                'stop' => $options['stop'] ?? null,
            ];

            Yii::info('Groq Streaming Request: ' . json_encode($data), 'groq');

            $response = $this->client->post('openai/v1/chat/completions', [
                'json' => $data,
                'stream' => true,
            ]);

            $body = $response->getBody();
            $buffer = '';
            
            while (!$body->eof()) {
                $chunk = $body->read(1024);
                $buffer .= $chunk;
                
                // Обрабатываем строки
                while (($pos = strpos($buffer, "\n")) !== false) {
                    $line = substr($buffer, 0, $pos);
                    $buffer = substr($buffer, $pos + 1);
                    
                    $line = trim($line);
                    if (empty($line)) continue;
                    
                    if (strpos($line, 'data: ') === 0) {
                        $jsonData = trim(substr($line, 6));
                        
                        if ($jsonData === '[DONE]') {
                            break 2; // Выходим из обоих циклов
                        }
                        
                        $chunkData = json_decode($jsonData, true);
                        if ($chunkData && isset($chunkData['choices'][0]['delta']['content'])) {
                            $content = $chunkData['choices'][0]['delta']['content'];
                            if (!empty($content)) {
                                $callback($content);
                            }
                        }
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            Yii::error('Groq Streaming Error: ' . $e->getMessage(), 'groq');
            return false;
        }
    }

    /**
     * Get available Groq models with their capabilities
     *
     * @return array Common Groq models
     */
    public function getAvailableModels()
    {
        return [
            'llama-3.1-8b-instant' => [
                'name' => 'Llama 3.1 8B (Instant)',
                'context_window' => 131072,
                'description' => 'Fast and efficient for most tasks'
            ],
            'llama-3.1-70b-versatile' => [
                'name' => 'Llama 3.1 70B (Versatile)',
                'context_window' => 131072,
                'description' => 'More capable, better reasoning'
            ],
            'llama-3.2-1b-preview' => [
                'name' => 'Llama 3.2 1B (Preview)',
                'context_window' => 131072,
                'description' => 'Lightweight and fast'
            ],
            'llama-3.2-3b-preview' => [
                'name' => 'Llama 3.2 3B (Preview)',
                'context_window' => 131072,
                'description' => 'Balanced performance'
            ],
            'mixtral-8x7b-32768' => [
                'name' => 'Mixtral 8x7B',
                'context_window' => 32768,
                'description' => 'Mixture of experts model'
            ],
            'gemma2-9b-it' => [
                'name' => 'Gemma 2 9B',
                'context_window' => 8192,
                'description' => 'Google\'s Gemma model'
            ]
        ];
    }

    /**
     * Validate message format for Groq API
     *
     * @param array $messages
     * @return bool
     */
    public function validateMessages($messages)
    {
        if (!is_array($messages) || empty($messages)) {
            return false;
        }

        foreach ($messages as $message) {
            if (!isset($message['role']) || !isset($message['content'])) {
                return false;
            }

            if (!in_array($message['role'], ['system', 'user', 'assistant'])) {
                return false;
            }
        }

        return true;
    }
}
