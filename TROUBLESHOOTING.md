# Диагностика проблем Telegram бота

## Быстрая диагностика

Запустите команду для автоматической проверки всех компонентов:

```bash
php yii bot/diagnose
```

## Пошаговая диагностика

### 1. Проверка компонентов

```bash
# Проверка всех компонентов
php yii bot/test-bot

# Тест только Groq API
php yii bot/groq-test

# Тест только Telegram API
php yii bot/webhook-info
```

### 2. Тестирование сообщений

```bash
# Тест обработки сообщения (замените CHAT_ID на ваш)
php yii bot/test-message CHAT_ID "Привет, бот!"

# Пример:
php yii bot/test-message 123456789 "Как дела?"
```

### 3. Работа с логами

```bash
# Показать последние 50 строк лога
php yii bot/show-logs

# Показать последние 100 строк
php yii bot/show-logs 100

# Очистить логи
php yii bot/clear-logs

# Мониторинг в реальном времени
php yii bot/tail-logs
```

## Частые проблемы и решения

### Проблема: Бот не отвечает на сообщения

**Возможные причины:**

1. **Неправильный токен бота**
   ```bash
   # Проверьте токен в config/params.php
   php yii bot/webhook-info
   ```

2. **Проблемы с Groq API**
   ```bash
   # Проверьте API ключ
   php yii bot/groq-test
   ```

3. **Ошибки в webhook**
   ```bash
   # Проверьте статус webhook
   php yii bot/webhook-info
   
   # Посмотрите логи
   php yii bot/show-logs
   ```

### Проблема: Ошибки в логах

**Типичные ошибки:**

1. **"Invalid token"** - неправильный токен бота
2. **"Groq API Error"** - проблемы с API ключом Groq
3. **"Database connection failed"** - проблемы с БД
4. **"Webhook not set"** - webhook не настроен

### Проблема: Медленные ответы

**Диагностика:**

```bash
# Проверьте время ответа в логах
php yii bot/test-message CHAT_ID "Тест скорости"
php yii bot/show-logs | grep "Response Time"
```

**Оптимизация:**
- Используйте более быструю модель Groq
- Уменьшите размер контекста
- Проверьте интернет-соединение

## Структура логов

Лог файл: `logs/bot.log`

**Типы записей:**
- `=== WEBHOOK REQUEST RECEIVED ===` - входящий запрос
- `=== PROCESSING MESSAGE ===` - обработка сообщения
- `=== GROQ API REQUEST ===` - запрос к Groq
- `=== TELEGRAM API REQUEST ===` - запрос к Telegram
- `ERROR:` - ошибки
- `SUCCESS:` - успешные операции

## Полезные команды

```bash
# Полная диагностика
php yii bot/diagnose

# Информация о webhook
php yii bot/webhook-info

# Удалить webhook (переход в polling режим)
php yii bot/delete-webhook

# Тест всех моделей Groq
php yii bot/show-models

# Прямой тест Groq API
php yii bot/direct-test
```

## Настройка webhook для разработки

1. Установите ngrok:
   ```bash
   # Скачайте с https://ngrok.com/
   ngrok http 80
   ```

2. Настройте webhook:
   ```bash
   # Замените URL на ваш ngrok URL
   curl -X POST "https://api.telegram.org/bot<YOUR_TOKEN>/setWebhook" \
        -d "url=https://your-ngrok-url.ngrok.io/telegram/webhook/<YOUR_TOKEN>"
   ```

## Мониторинг в продакшене

```bash
# Постоянный мониторинг логов
php yii bot/tail-logs

# Периодическая проверка статуса
watch -n 30 'php yii bot/diagnose'

# Проверка размера лога
ls -lh logs/bot.log
```

## Контакты для поддержки

При возникновении проблем:
1. Запустите `php yii bot/diagnose`
2. Сохраните вывод команды
3. Приложите последние строки из `php yii bot/show-logs`
4. Опишите шаги для воспроизведения проблемы