<?php

namespace app\models;

use Yii;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "users".
 *
 * @property int $id
 * @property int $telegram_id
 * @property string|null $username
 * @property string|null $first_name
 * @property string|null $last_name
 * @property string|null $language_code
 * @property bool $is_bot
 * @property bool $is_premium
 * @property string $created_at
 * @property string $updated_at
 *
 * @property Conversation[] $conversations
 */
class User extends ActiveRecord implements \yii\web\IdentityInterface
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'users';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['telegram_id'], 'required'],
            [['telegram_id'], 'integer'],
            [['telegram_id'], 'unique'],
            [['is_bot', 'is_premium'], 'boolean'],
            [['created_at', 'updated_at'], 'safe'],
            [['username', 'first_name', 'last_name'], 'string', 'max' => 255],
            [['language_code'], 'string', 'max' => 10],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'telegram_id' => 'Telegram ID',
            'username' => 'Username',
            'first_name' => 'First Name',
            'last_name' => 'Last Name',
            'language_code' => 'Language Code',
            'is_bot' => 'Is Bot',
            'is_premium' => 'Is Premium',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for the associated conversations.
     *
     * @return \yii\db\ActiveQuery
     */
    public function getConversations()
    {
        return $this->hasMany(Conversation::class, ['user_id' => 'id']);
    }

    /**
     * Gets query for the associated memories.
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMemories()
    {
        return $this->hasMany(UserMemory::class, ['user_id' => 'id']);
    }

    /**
     * Finds user by telegram_id
     *
     * @param int $telegramId
     * @return static|null
     */
    public static function findByTelegramId($telegramId)
    {
        return static::findOne(['telegram_id' => $telegramId]);
    }

    /**
     * Creates or updates user from Telegram data
     *
     * @param array $userData
     * @return static
     */
    public static function createOrUpdate($userData)
    {
        $user = static::findByTelegramId($userData['id']);
        
        if (!$user) {
            $user = new static();
            $user->telegram_id = $userData['id'];
            $user->created_at = date('Y-m-d H:i:s');
        }
        
        $user->username = $userData['username'] ?? null;
        $user->first_name = $userData['first_name'] ?? null;
        $user->last_name = $userData['last_name'] ?? null;
        $user->language_code = $userData['language_code'] ?? null;
        $user->is_bot = $userData['is_bot'] ?? false;
        $user->is_premium = $userData['is_premium'] ?? false;
        $user->updated_at = date('Y-m-d H:i:s');
        
        $user->save();
        
        return $user;
    }

    // IdentityInterface implementation
    public static function findIdentity($id)
    {
        return static::findOne($id);
    }

    public static function findIdentityByAccessToken($token, $type = null)
    {
        return null;
    }

    public function getId()
    {
        return $this->id;
    }

    public function getAuthKey()
    {
        return null;
    }

    public function validateAuthKey($authKey)
    {
        return false;
    }
}