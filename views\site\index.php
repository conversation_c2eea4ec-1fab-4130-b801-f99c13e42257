<?php

/* @var $this yii\web\View */
/* @var $botInfo array */
/* @var $webhookInfo array */

use yii\helpers\Html;

$this->title = 'Telegram Bot with Groq API';
?>
<div class="site-index">

    <div class="jumbotron text-center bg-transparent">
        <h1 class="display-4">🤖 Telegram Bot</h1>
        <p class="lead">AI-бот с памятью на базе Groq API</p>
    </div>

    <div class="body-content">

        <div class="row">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h3>🤖 Информация о боте</h3>
                    </div>
                    <div class="card-body">
                        <?php if ($botInfo): ?>
                            <table class="table table-striped">
                                <tr>
                                    <td><strong>ID:</strong></td>
                                    <td><?= Html::encode($botInfo['id']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Username:</strong></td>
                                    <td>@<?= Html::encode($botInfo['username']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>First Name:</strong></td>
                                    <td><?= Html::encode($botInfo['first_name']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Is Bot:</strong></td>
                                    <td><?= $botInfo['is_bot'] ? 'Да' : 'Нет' ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Can Join Groups:</strong></td>
                                    <td><?= $botInfo['can_join_groups'] ? 'Да' : 'Нет' ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Can Read All Group Messages:</strong></td>
                                    <td><?= $botInfo['can_read_all_group_messages'] ? 'Да' : 'Нет' ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Supports Inline Queries:</strong></td>
                                    <td><?= $botInfo['supports_inline_queries'] ? 'Да' : 'Нет' ?></td>
                                </tr>
                            </table>
                        <?php else: ?>
                            <div class="alert alert-danger">
                                ❌ Не удалось получить информацию о боте. Проверьте токен.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h3>🔗 Webhook</h3>
                    </div>
                    <div class="card-body">
                        <?php if ($webhookInfo): ?>
                            <table class="table table-striped">
                                <tr>
                                    <td><strong>URL:</strong></td>
                                    <td>
                                        <?php if (!empty($webhookInfo['url'])): ?>
                                            <span class="text-success">✅ Установлен</span><br>
                                            <small class="text-muted"><?= Html::encode($webhookInfo['url']) ?></small>
                                        <?php else: ?>
                                            <span class="text-warning">⚠️ Не установлен</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Has Custom Certificate:</strong></td>
                                    <td><?= $webhookInfo['has_custom_certificate'] ? 'Да' : 'Нет' ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Pending Update Count:</strong></td>
                                    <td><?= $webhookInfo['pending_update_count'] ?></td>
                                </tr>
                                <?php if (isset($webhookInfo['last_error_date'])): ?>
                                <tr>
                                    <td><strong>Last Error Date:</strong></td>
                                    <td><?= date('d.m.Y H:i:s', $webhookInfo['last_error_date']) ?></td>
                                </tr>
                                <?php endif; ?>
                                <?php if (isset($webhookInfo['last_error_message'])): ?>
                                <tr>
                                    <td><strong>Last Error:</strong></td>
                                    <td><span class="text-danger"><?= Html::encode($webhookInfo['last_error_message']) ?></span></td>
                                </tr>
                                <?php endif; ?>
                            </table>
                        <?php else: ?>
                            <div class="alert alert-danger">
                                ❌ Не удалось получить информацию о webhook.
                            </div>
                        <?php endif; ?>

                        <div class="mt-3">
                            <?= Html::a('🔗 Установить Webhook', ['/telegram/set-webhook'], ['class' => 'btn btn-primary']) ?>
                            <?= Html::a('ℹ️ Информация о Webhook', ['/telegram/webhook-info'], ['class' => 'btn btn-info']) ?>
                            <?= Html::a('🗑 Удалить Webhook', ['/telegram/delete-webhook'], ['class' => 'btn btn-danger']) ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h3>🧪 Тестирование</h3>
                    </div>
                    <div class="card-body">
                        <p>Проверьте работу компонентов системы:</p>
                        <div class="btn-group" role="group">
                            <?= Html::a('🤖 Тест Telegram API', ['/site/test-telegram'], ['class' => 'btn btn-outline-primary']) ?>
                            <?= Html::a('🧠 Тест Groq API', ['/site/test-groq'], ['class' => 'btn btn-outline-success']) ?>
                            <?= Html::a('💾 Память пользователей', ['/site/memory'], ['class' => 'btn btn-outline-info']) ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h3>📋 Инструкция по настройке</h3>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li><strong>Настройте параметры</strong> в файле <code>config/params.php</code>:
                                <ul>
                                    <li><code>telegramBotToken</code> - токен вашего Telegram бота</li>
                                    <li><code>groqApiKey</code> - API ключ от Groq</li>
                                    <li><code>webhookUrl</code> - URL для webhook</li>
                                </ul>
                            </li>
                            <li><strong>Выполните миграции</strong>: <code>php yii migrate</code></li>
                            <li><strong>Установите webhook</strong> нажав кнопку выше</li>
                            <li><strong>Начните общение</strong> с ботом в Telegram</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>