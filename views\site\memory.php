<?php

/* @var $this yii\web\View */
/* @var $users app\models\User[] */

use yii\helpers\Html;
use app\models\UserMemory;

$this->title = 'User Memory';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="site-memory">
    <h1><?= Html::encode($this->title) ?></h1>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h3>🧠 Память пользователей</h3>
                </div>
                <div class="card-body">
                    <?php if (empty($users)): ?>
                        <div class="alert alert-info">
                            📭 Пока нет пользователей с сохраненной информацией.
                        </div>
                    <?php else: ?>
                        <?php $categories = UserMemory::getCategories(); ?>
                        
                        <?php foreach ($users as $user): ?>
                            <?php if (!empty($user->memories)): ?>
                                <div class="card mb-3">
                                    <div class="card-header">
                                        <h5>
                                            👤 <?= Html::encode($user->first_name ?: 'User') ?>
                                            <?php if ($user->username): ?>
                                                (@<?= Html::encode($user->username) ?>)
                                            <?php endif; ?>
                                            <small class="text-muted">ID: <?= $user->telegram_id ?></small>
                                        </h5>
                                        <small class="text-muted">
                                            Зарегистрирован: <?= date('d.m.Y H:i', strtotime($user->created_at)) ?>
                                            | Фактов сохранено: <?= count($user->memories) ?>
                                        </small>
                                    </div>
                                    <div class="card-body">
                                        <?php
                                        $memoriesByCategory = [];
                                        foreach ($user->memories as $memory) {
                                            $memoriesByCategory[$memory->category][] = $memory;
                                        }
                                        ?>
                                        
                                        <?php foreach ($memoriesByCategory as $category => $memories): ?>
                                            <div class="mb-3">
                                                <h6 class="text-primary">
                                                    📁 <?= Html::encode($categories[$category] ?? $category) ?>
                                                </h6>
                                                <div class="row">
                                                    <?php foreach ($memories as $memory): ?>
                                                        <div class="col-md-6 mb-2">
                                                            <div class="border rounded p-2 bg-light">
                                                                <strong><?= Html::encode($memory->key_name) ?>:</strong>
                                                                <?= Html::encode($memory->value) ?>
                                                                <br>
                                                                <small class="text-muted">
                                                                    Важность: <?= str_repeat('⭐', min($memory->importance, 5)) ?>
                                                                    | <?= date('d.m H:i', strtotime($memory->updated_at)) ?>
                                                                </small>
                                                            </div>
                                                        </div>
                                                    <?php endforeach; ?>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    <?php endif; ?>
                    
                    <div class="mt-3">
                        <?= Html::a('🏠 На главную', ['/'], ['class' => 'btn btn-primary']) ?>
                        <?= Html::a('🔄 Обновить', ['/site/memory'], ['class' => 'btn btn-secondary']) ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h3>📊 Статистика памяти</h3>
                </div>
                <div class="card-body">
                    <?php
                    $totalUsers = count($users);
                    $totalMemories = 0;
                    $categoriesStats = [];
                    
                    foreach ($users as $user) {
                        $totalMemories += count($user->memories);
                        foreach ($user->memories as $memory) {
                            if (!isset($categoriesStats[$memory->category])) {
                                $categoriesStats[$memory->category] = 0;
                            }
                            $categoriesStats[$memory->category]++;
                        }
                    }
                    ?>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Общая статистика</h5>
                            <ul class="list-unstyled">
                                <li>👥 Пользователей с памятью: <?= $totalUsers ?></li>
                                <li>🧠 Всего фактов: <?= $totalMemories ?></li>
                                <li>📈 Среднее на пользователя: <?= $totalUsers > 0 ? round($totalMemories / $totalUsers, 1) : 0 ?></li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>По категориям</h5>
                            <ul class="list-unstyled">
                                <?php foreach ($categoriesStats as $category => $count): ?>
                                    <li>📁 <?= Html::encode($categories[$category] ?? $category) ?>: <?= $count ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>