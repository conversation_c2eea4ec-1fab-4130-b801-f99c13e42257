<?php
// Простой тест webhook без Yii
header('Content-Type: application/json');

$input = file_get_contents('php://input');
$data = json_decode($input, true);

$response = [
    'ok' => true,
    'message' => 'Webhook получен!',
    'timestamp' => date('Y-m-d H:i:s'),
    'data' => $data
];

// Логируем в файл
$logFile = __DIR__ . '/logs/webhook_test.log';
$logMessage = date('Y-m-d H:i:s') . " - Webhook получен: " . $input . "\n";
file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);

echo json_encode($response);
?>