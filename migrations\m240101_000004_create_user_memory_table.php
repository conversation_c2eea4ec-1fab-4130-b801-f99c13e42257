<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%user_memory}}`.
 */
class m240101_000004_create_user_memory_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%user_memory}}', [
            'id' => $this->primaryKey(),
            'user_id' => $this->integer()->notNull(),
            'category' => $this->string(100)->notNull(), // personal, preferences, facts, etc.
            'key_name' => $this->string(255)->notNull(), // name, age, hobby, etc.
            'value' => $this->text()->notNull(),
            'importance' => $this->integer()->defaultValue(1), // 1-10 scale
            'source_message_id' => $this->integer(),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'updated_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
        ]);

        $this->addForeignKey(
            'fk-user_memory-user_id',
            '{{%user_memory}}',
            'user_id',
            '{{%users}}',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-user_memory-source_message_id',
            '{{%user_memory}}',
            'source_message_id',
            '{{%messages}}',
            'id',
            'SET NULL'
        );

        $this->createIndex('idx-user_memory-user_id', '{{%user_memory}}', 'user_id');
        $this->createIndex('idx-user_memory-category', '{{%user_memory}}', 'category');
        $this->createIndex('idx-user_memory-importance', '{{%user_memory}}', 'importance');
        $this->createIndex('idx-user_memory-key_name', '{{%user_memory}}', 'key_name');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('fk-user_memory-source_message_id', '{{%user_memory}}');
        $this->dropForeignKey('fk-user_memory-user_id', '{{%user_memory}}');
        $this->dropTable('{{%user_memory}}');
    }
}