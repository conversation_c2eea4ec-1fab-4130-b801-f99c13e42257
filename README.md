# Telegram Bot with Groq API

AI-бот для Telegram с памятью, построенный на Yii2 framework и использующий Groq API для генерации ответов.

## Возможности

- 🤖 **Интеграция с Telegram Bot API** - полная поддержка webhook
- 🧠 **AI-ответы через Groq API** - использование мощных языковых моделей
- 💾 **Память разговоров** - бот запоминает контекст диалога
- 📊 **Статистика** - отслеживание использования токенов и сообщений
- 🔧 **Веб-панель управления** - удобный интерфейс для настройки
- 🛡️ **Безопасность** - защищенное хранение API ключей

## Технические особенности

- **Framework**: Yii2 Basic
- **База данных**: SQLite (легко переключить на MySQL/PostgreSQL)
- **AI API**: Groq (совместим с OpenAI API)
- **Архитектура**: MVC с компонентами для внешних API
- **Логирование**: Полное логирование всех операций

## Быстрый старт

### 1. Установка зависимостей

```bash
composer install
```

### 2. Настройка параметров

Отредактируйте файл `config/params.php`:

```php
return [
    'telegramBotToken' => 'YOUR_TELEGRAM_BOT_TOKEN_HERE',
    'groqApiKey' => 'YOUR_GROQ_API_KEY_HERE',
    'webhookUrl' => 'https://yourdomain.com/webhook/YOUR_TELEGRAM_BOT_TOKEN_HERE',
    'adminChatId' => 'YOUR_ADMIN_CHAT_ID',
];
```

### 3. Создание базы данных

```bash
php yii migrate
```

### 4. Настройка веб-сервера

Убедитесь, что корневая директория веб-сервера указывает на папку `web/`.

### 5. Установка webhook

Перейдите на главную страницу вашего сайта и нажмите кнопку "Установить Webhook".

## Структура проекта

```
├── components/           # Компоненты для работы с API
│   ├── GroqApiComponent.php
│   └── TelegramBotComponent.php
├── config/              # Конфигурационные файлы
│   ├── web.php
│   ├── console.php
│   ├── db.php
│   └── params.php
├── controllers/         # Контроллеры
│   ├── SiteController.php
│   └── TelegramController.php
├── migrations/          # Миграции базы данных
├── models/             # Модели данных
│   ├── User.php
│   ├── Conversation.php
│   └── Message.php
├── views/              # Представления
├── web/                # Публичная директория
└── runtime/            # Временные файлы и БД
```

## Команды бота

- `/start` - Начать работу с ботом
- `/help` - Показать справку
- `/clear` - Очистить историю разговора
- `/stats` - Показать статистику

## API компоненты

### GroqApiComponent

Компонент для работы с Groq API:

```php
$response = Yii::$app->groqApi->generateResponse($userMessage, $context);
$text = Yii::$app->groqApi->extractResponseText($response);
```

### TelegramBotComponent

Компонент для работы с Telegram Bot API:

```php
Yii::$app->telegramBot->sendMessage($chatId, $text);
Yii::$app->telegramBot->setWebhook($url);
```

## Модели данных

### User
Хранит информацию о пользователях Telegram.

### Conversation
Управляет контекстом разговоров между пользователями и ботом.

### Message
Хранит все сообщения в разговорах с информацией о ролях и использованных токенах.

## Конфигурация

### Настройка базы данных

По умолчанию используется SQLite. Для изменения на MySQL/PostgreSQL отредактируйте `config/db.php`:

```php
return [
    'class' => 'yii\db\Connection',
    'dsn' => 'mysql:host=localhost;dbname=telegram_bot',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8',
];
```

### Настройка логирования

Логи сохраняются в `runtime/logs/`:
- `app.log` - общие логи приложения
- `telegram.log` - логи взаимодействия с Telegram API

### Настройка Groq API

Поддерживаемые модели:
- `llama3-8b-8192` (по умолчанию)
- `llama3-70b-8192`
- `mixtral-8x7b-32768`

Изменить модель можно в компоненте:

```php
'groqApi' => [
    'class' => 'app\components\GroqApiComponent',
    'apiKey' => $params['groqApiKey'],
    'model' => 'llama3-70b-8192',
    'maxTokens' => 2048,
    'temperature' => 0.7,
],
```

## Безопасность

- Все API ключи хранятся в `config/params.php`
- CSRF защита отключена только для webhook endpoint
- Валидация токена webhook
- Логирование всех операций

## Мониторинг

Веб-панель предоставляет:
- Статус бота и webhook
- Тестирование API соединений
- Просмотр логов
- Управление webhook

## Разработка

### Добавление новых команд

Добавьте обработку в `TelegramController::handleCommand()`:

```php
case '/newcommand':
    $telegramBot->sendMessage($chatId, 'Ответ на новую команду');
    break;
```

### Расширение функциональности AI

Модифицируйте системный промпт в `GroqApiComponent::generateResponse()`:

```php
$systemMessage = [
    'role' => 'system',
    'content' => 'Ваш кастомный системный промпт'
];
```

## Лицензия

MIT License

## Поддержка

Для вопросов и предложений создавайте issues в репозитории проекта.