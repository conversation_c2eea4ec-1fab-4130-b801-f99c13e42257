<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%conversations}}`.
 */
class m240101_000002_create_conversations_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%conversations}}', [
            'id' => $this->primaryKey(),
            'user_id' => $this->integer()->notNull(),
            'chat_id' => $this->bigInteger()->notNull(),
            'context' => $this->text(), // JSON с контекстом разговора
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'updated_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
        ]);

        $this->addForeignKey(
            'fk-conversations-user_id',
            '{{%conversations}}',
            'user_id',
            '{{%users}}',
            'id',
            'CASCADE'
        );

        $this->createIndex('idx-conversations-user_id', '{{%conversations}}', 'user_id');
        $this->createIndex('idx-conversations-chat_id', '{{%conversations}}', 'chat_id');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('fk-conversations-user_id', '{{%conversations}}');
        $this->dropTable('{{%conversations}}');
    }
}