<?php

/* @var $this yii\web\View */
/* @var $result array */

use yii\helpers\Html;
use yii\helpers\VarDumper;

$this->title = 'Test Groq API';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="site-test-groq">
    <h1><?= Html::encode($this->title) ?></h1>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h3>🧠 Тест Groq API</h3>
                </div>
                <div class="card-body">
                    <?php if ($result['response']): ?>
                        <div class="alert alert-success">
                            ✅ Соединение с Groq API установлено успешно!
                        </div>
                        
                        <h4>Запрос:</h4>
                        <div class="bg-light p-3 rounded">
                            <?= Html::encode($result['request']) ?>
                        </div>
                        
                        <h4 class="mt-3">Ответ:</h4>
                        <div class="bg-light p-3 rounded">
                            <?= Html::encode($result['extracted_text']) ?>
                        </div>
                        
                        <h4 class="mt-3">Использование токенов:</h4>
                        <table class="table table-striped">
                            <tr>
                                <td><strong>Prompt Tokens:</strong></td>
                                <td><?= $result['token_usage']['prompt_tokens'] ?></td>
                            </tr>
                            <tr>
                                <td><strong>Completion Tokens:</strong></td>
                                <td><?= $result['token_usage']['completion_tokens'] ?></td>
                            </tr>
                            <tr>
                                <td><strong>Total Tokens:</strong></td>
                                <td><?= $result['token_usage']['total_tokens'] ?></td>
                            </tr>
                        </table>
                        
                        <details class="mt-3">
                            <summary>Полный ответ API</summary>
                            <pre class="bg-light p-3 rounded mt-2"><?= VarDumper::dumpAsString($result['response'], 10, true) ?></pre>
                        </details>
                    <?php else: ?>
                        <div class="alert alert-danger">
                            ❌ Ошибка соединения с Groq API. Проверьте настройки.
                        </div>
                    <?php endif; ?>
                    
                    <div class="mt-3">
                        <?= Html::a('🏠 На главную', ['/'], ['class' => 'btn btn-primary']) ?>
                        <?= Html::a('🔄 Повторить тест', ['/site/test-groq'], ['class' => 'btn btn-secondary']) ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>