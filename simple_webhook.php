<?php
// Простой webhook для тестирования
header('Content-Type: application/json');

// Получаем данные
$input = file_get_contents('php://input');
$update = json_decode($input, true);

// Логируем
$logFile = __DIR__ . '/logs/simple_webhook.log';
$logMessage = date('Y-m-d H:i:s') . " - Получен webhook: " . $input . "\n";
file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);

// Простой ответ
echo json_encode(['ok' => true, 'status' => 'received']);
?>