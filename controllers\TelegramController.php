<?php

namespace app\controllers;

use Yii;
use yii\web\Controller;
use yii\web\Response;
use yii\web\BadRequestHttpException;
use app\models\User;
use app\models\Conversation;
use app\models\Message;
use app\models\UserMemory;

/**
 * Telegram Controller handles webhook requests from Telegram
 */
class TelegramController extends Controller
{
    /**
     * {@inheritdoc}
     */
    public function beforeAction($action)
    {
        // Disable CSRF validation for webhook
        $this->enableCsrfValidation = false;
        return parent::beforeAction($action);
    }

    /**
     * Webhook endpoint for Telegram
     *
     * @param string $token
     * @return Response
     * @throws BadRequestHttpException
     */
    public function actionWebhook($token)
    {
        $startTime = microtime(true);
        
        $this->logToFile("=== WEBHOOK REQUEST RECEIVED ===");
        $this->logToFile("Token provided: " . ($token ? substr($token, 0, 10) . '...' : 'EMPTY'));
        $this->logToFile("Expected token: " . (Yii::$app->params['telegramBotToken'] ? substr(Yii::$app->params['telegramBotToken'], 0, 10) . '...' : 'NOT SET'));
        
        // Verify token
        if ($token !== Yii::$app->params['telegramBotToken']) {
            $this->logToFile("ERROR: Invalid token provided");
            throw new BadRequestHttpException('Invalid token');
        }

        // Get update data
        $input = file_get_contents('php://input');
        $this->logToFile("Raw input length: " . strlen($input));
        $this->logToFile("Raw input: " . $input);
        
        $update = json_decode($input, true);

        if (!$update) {
            $this->logToFile("ERROR: Invalid JSON in webhook request");
            $this->logToFile("JSON decode error: " . json_last_error_msg());
            throw new BadRequestHttpException('Invalid JSON');
        }

        $this->logToFile("Update parsed successfully");
        $this->logToFile("Update data: " . json_encode($update, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));

        Yii::info('Telegram webhook received: ' . $input, 'telegram');

        try {
            $this->processUpdate($update);
            $processingTime = round((microtime(true) - $startTime) * 1000, 2);
            $this->logToFile("SUCCESS: Webhook processed in {$processingTime}ms");
        } catch (\Exception $e) {
            $processingTime = round((microtime(true) - $startTime) * 1000, 2);
            $this->logToFile("EXCEPTION in webhook processing: " . $e->getMessage());
            $this->logToFile("File: " . $e->getFile() . ":" . $e->getLine());
            $this->logToFile("Processing time before exception: {$processingTime}ms");
            
            Yii::error('Error processing Telegram update: ' . $e->getMessage(), 'telegram');
        }

        $this->logToFile(""); // Empty line for separation

        Yii::$app->response->format = Response::FORMAT_JSON;
        return ['ok' => true];
    }

    /**
     * Sets webhook URL
     *
     * @return Response
     */
    public function actionSetWebhook()
    {
        $telegramBot = Yii::$app->telegramBot;
        $result = $telegramBot->setWebhook();

        Yii::$app->response->format = Response::FORMAT_JSON;
        
        if ($result) {
            return [
                'success' => true,
                'message' => 'Webhook set successfully',
                'data' => $result,
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Failed to set webhook',
            ];
        }
    }

    /**
     * Gets webhook info
     *
     * @return Response
     */
    public function actionWebhookInfo()
    {
        $telegramBot = Yii::$app->telegramBot;
        $result = $telegramBot->getWebhookInfo();

        Yii::$app->response->format = Response::FORMAT_JSON;
        return $result ?: ['error' => 'Failed to get webhook info'];
    }

    /**
     * Deletes webhook
     *
     * @return Response
     */
    public function actionDeleteWebhook()
    {
        $telegramBot = Yii::$app->telegramBot;
        $result = $telegramBot->deleteWebhook();

        Yii::$app->response->format = Response::FORMAT_JSON;
        
        if ($result) {
            return [
                'success' => true,
                'message' => 'Webhook deleted successfully',
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Failed to delete webhook',
            ];
        }
    }

    /**
     * Processes Telegram update
     *
     * @param array $update
     */
    private function processUpdate($update)
    {
        if (isset($update['message'])) {
            $this->processMessage($update['message']);
        } elseif (isset($update['callback_query'])) {
            $this->processCallbackQuery($update['callback_query']);
        }
    }

    /**
     * Processes incoming message
     *
     * @param array $message
     */
    private function processMessage($message)
    {
        $startTime = microtime(true);
        
        $this->logToFile("=== PROCESSING MESSAGE ===");
        $this->logToFile("Message data: " . json_encode($message, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));

        $telegramBot = Yii::$app->telegramBot;
        $groqApi = Yii::$app->groqApi;
        $memoryManager = Yii::$app->memoryManager;

        $chatId = $message['chat']['id'];
        $messageId = $message['message_id'];
        $text = $message['text'] ?? '';
        $from = $message['from'];

        $this->logToFile("Chat ID: $chatId");
        $this->logToFile("Message ID: $messageId");
        $this->logToFile("Text: $text");
        $this->logToFile("From: " . json_encode($from, JSON_UNESCAPED_UNICODE));

        try {
            // Create or update user
            $this->logToFile("Creating/updating user...");
            $user = User::createOrUpdate($from);
            $this->logToFile("User ID: " . $user->id);

            // Get or create conversation
            $this->logToFile("Getting/creating conversation...");
            $conversation = Conversation::getOrCreate($user->id, $chatId);
            $this->logToFile("Conversation ID: " . $conversation->id);

            // Handle commands
            if (strpos($text, '/') === 0) {
                $this->logToFile("Processing command: $text");
                $this->handleCommand($text, $chatId, $conversation, $telegramBot, $user);
                return;
            }

            // Send typing action
            $this->logToFile("Sending typing action...");
            $typingResult = $telegramBot->sendChatAction($chatId);
            $this->logToFile("Typing action result: " . json_encode($typingResult));

            // Save user message
            $this->logToFile("Saving user message...");
            $userMessage = $conversation->addMessage(Message::ROLE_USER, $text, $messageId);
            $this->logToFile("User message saved with ID: " . $userMessage->id);

            // Extract memory from user message
            $this->logToFile("Extracting memory from message...");
            $extractedMemories = $memoryManager->extractMemoryFromMessage($text, $user->id, $userMessage->id);
            
            // Log extracted memories
            if (!empty($extractedMemories)) {
                $this->logToFile('Extracted ' . count($extractedMemories) . ' memory items');
                Yii::info('Extracted ' . count($extractedMemories) . ' memory items from message', 'telegram');
            } else {
                $this->logToFile('No memory items extracted');
            }

            // Get conversation context
            $this->logToFile("Getting conversation context...");
            $context = $conversation->getContextForGPT(8); // Reduced to make room for memory
            $this->logToFile("Context messages count: " . count($context));

            // Get user memory context
            $this->logToFile("Getting user memory context...");
            $memoryContext = $memoryManager->getMemoryContext($user->id);
            $this->logToFile("Memory context length: " . strlen($memoryContext ?? ''));

            // Enhanced system prompt with memory
            $systemPrompt = 'Ты полезный AI-ассистент. Отвечай на русском языке, будь дружелюбным и полезным. ';
            if ($memoryContext) {
                $systemPrompt .= 'Используй следующую информацию о пользователе для более персонализированных ответов:\n' . $memoryContext;
            }
            $this->logToFile("System prompt length: " . strlen($systemPrompt));

            // Generate response using Groq API with memory context
            $this->logToFile("Generating response with Groq API...");
            $groqStartTime = microtime(true);
            
            $response = $groqApi->generateResponse($text, $context, [
                'system_prompt' => $systemPrompt
            ]);
            
            $groqTime = round((microtime(true) - $groqStartTime) * 1000, 2);
            $this->logToFile("Groq API response time: {$groqTime}ms");

            if ($response) {
                $this->logToFile("Groq API response received");
                $this->logToFile("Response data: " . json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
                
                $responseText = $groqApi->extractResponseText($response);
                $tokenUsage = $groqApi->getTokenUsage($response);

                $this->logToFile("Extracted response text length: " . strlen($responseText ?? ''));
                $this->logToFile("Token usage: " . json_encode($tokenUsage));

                if ($responseText) {
                    // Send response to user
                    $this->logToFile("Sending response to user...");
                    $sendStartTime = microtime(true);
                    
                    $sentMessage = $telegramBot->sendMessage($chatId, $responseText);
                    
                    $sendTime = round((microtime(true) - $sendStartTime) * 1000, 2);
                    $this->logToFile("Message send time: {$sendTime}ms");
                    $this->logToFile("Sent message result: " . json_encode($sentMessage));

                    if ($sentMessage) {
                        // Save assistant message
                        $this->logToFile("Saving assistant message...");
                        $assistantMessageId = $sentMessage['message_id'] ?? 0;
                        $assistantMessage = $conversation->addMessage(
                            Message::ROLE_ASSISTANT,
                            $responseText,
                            $assistantMessageId,
                            $tokenUsage['total_tokens'] ?? 0
                        );
                        $this->logToFile("Assistant message saved with ID: " . $assistantMessage->id);

                        // Clean old messages to maintain context size
                        $this->logToFile("Cleaning old messages...");
                        Message::cleanOldMessages($conversation->id, 20);
                        
                        $totalTime = round((microtime(true) - $startTime) * 1000, 2);
                        $this->logToFile("SUCCESS: Message processed successfully in {$totalTime}ms");
                    } else {
                        $this->logToFile("ERROR: Failed to send message to Telegram");
                        $telegramBot->sendMessage($chatId, '❌ Извините, не удалось отправить ответ.');
                    }
                } else {
                    $this->logToFile("ERROR: No response text extracted from Groq API response");
                    $telegramBot->sendMessage($chatId, '❌ Извините, не удалось получить ответ от AI.');
                }
            } else {
                $this->logToFile("ERROR: No response from Groq API");
                $telegramBot->sendMessage($chatId, '❌ Произошла ошибка при обращении к AI. Попробуйте позже.');
            }
        } catch (\Exception $e) {
            $totalTime = round((microtime(true) - $startTime) * 1000, 2);
            $this->logToFile("EXCEPTION in processMessage: " . $e->getMessage());
            $this->logToFile("File: " . $e->getFile() . ":" . $e->getLine());
            $this->logToFile("Processing time before exception: {$totalTime}ms");
            
            Yii::error('Error processing message: ' . $e->getMessage(), 'telegram');
            
            try {
                $telegramBot->sendMessage($chatId, '❌ Произошла внутренняя ошибка. Попробуйте позже.');
            } catch (\Exception $sendException) {
                $this->logToFile("EXCEPTION sending error message: " . $sendException->getMessage());
            }
        }

        $this->logToFile(""); // Empty line for separation
    }

    /**
     * Logs message to bot-specific log file
     *
     * @param string $message
     */
    private function logToFile($message)
    {
        $logFile = Yii::getAlias('@app/logs/bot.log');
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] $message" . PHP_EOL;
        
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }

    /**
     * Processes callback query
     *
     * @param array $callbackQuery
     */
    private function processCallbackQuery($callbackQuery)
    {
        $telegramBot = Yii::$app->telegramBot;
        $chatId = $callbackQuery['message']['chat']['id'];
        $data = $callbackQuery['data'];

        // Handle different callback data
        switch ($data) {
            case 'clear_history':
                $user = User::findByTelegramId($callbackQuery['from']['id']);
                if ($user) {
                    $conversation = Conversation::findByUserAndChat($user->id, $chatId);
                    if ($conversation) {
                        $conversation->clearHistory();
                        $telegramBot->sendMessage($chatId, '✅ История разговора очищена.');
                    }
                }
                break;
        }

        // Answer callback query
        $telegramBot->makeRequest('answerCallbackQuery', [
            'callback_query_id' => $callbackQuery['id'],
        ]);
    }

    /**
     * Handles bot commands
     *
     * @param string $command
     * @param int $chatId
     * @param Conversation $conversation
     * @param object $telegramBot
     * @param User $user
     */
    private function handleCommand($command, $chatId, $conversation, $telegramBot, $user)
    {
        $command = strtolower(trim($command));
        $memoryManager = Yii::$app->memoryManager;

        switch ($command) {
            case '/start':
                $welcomeText = "👋 Привет! Я AI-бот с памятью.\n\n";
                $welcomeText .= "Я запоминаю важную информацию о тебе и использую её для более персонализированных ответов.\n\n";
                $welcomeText .= "Доступные команды:\n";
                $welcomeText .= "/help - показать справку\n";
                $welcomeText .= "/clear - очистить историю разговора\n";
                $welcomeText .= "/memory - показать сохраненную информацию\n";
                $welcomeText .= "/forget - забыть определенную информацию\n";
                $welcomeText .= "/stats - показать статистику\n\n";
                $welcomeText .= "Просто расскажи мне о себе, и я запомню! 😊";

                $telegramBot->sendMessage($chatId, $welcomeText);
                break;

            case '/help':
                $helpText = "🤖 <b>AI-бот с памятью</b>\n\n";
                $helpText .= "<b>Возможности:</b>\n";
                $helpText .= "• Запоминаю важную информацию о тебе\n";
                $helpText .= "• Использую эту информацию в ответах\n";
                $helpText .= "• Поддерживаю длительные диалоги\n";
                $helpText .= "• Отвечаю на вопросы используя GPT\n\n";
                $helpText .= "<b>Команды:</b>\n";
                $helpText .= "/start - начать работу с ботом\n";
                $helpText .= "/help - показать эту справку\n";
                $helpText .= "/clear - очистить историю разговора\n";
                $helpText .= "/memory - показать сохраненную информацию\n";
                $helpText .= "/forget [что забыть] - забыть информацию\n";
                $helpText .= "/stats - показать статистику\n\n";
                $helpText .= "Расскажи мне о себе, своих интересах, планах - я всё запомню!";

                $telegramBot->sendMessage($chatId, $helpText);
                break;

            case '/clear':
                $keyboard = $telegramBot->createInlineKeyboard([
                    [
                        ['text' => '✅ Да, очистить', 'callback_data' => 'clear_history'],
                        ['text' => '❌ Отмена', 'callback_data' => 'cancel'],
                    ],
                ]);

                $telegramBot->sendMessage(
                    $chatId,
                    '🗑 Вы уверены, что хотите очистить историю разговора?\n\n⚠️ Это не удалит сохраненную информацию о вас.',
                    $keyboard
                );
                break;

            case '/memory':
                $this->showUserMemory($chatId, $telegramBot, $user->id);
                break;

            case '/stats':
                $this->showStats($chatId, $telegramBot, $conversation, $user->id);
                break;

            default:
                // Check if it's a forget command
                if (strpos($command, '/forget') === 0) {
                    $this->handleForgetCommand($command, $chatId, $telegramBot, $user->id);
                } else {
                    $telegramBot->sendMessage($chatId, '❓ Неизвестная команда. Используйте /help для просмотра доступных команд.');
                }
                break;
        }
    }

    /**
     * Shows user memory
     *
     * @param int $chatId
     * @param object $telegramBot
     * @param int $userId
     */
    private function showUserMemory($chatId, $telegramBot, $userId)
    {
        $memoryManager = Yii::$app->memoryManager;
        $formattedMemory = $memoryManager->formatMemoryForDisplay($userId);

        if (empty($formattedMemory)) {
            $telegramBot->sendMessage($chatId, '🧠 У меня пока нет сохраненной информации о вас.\n\nРасскажите мне о себе, и я запомню!');
            return;
        }

        $memoryText = "🧠 <b>Сохраненная информация о вас:</b>\n\n";

        foreach ($formattedMemory as $categoryName => $items) {
            $memoryText .= "📁 <b>{$categoryName}:</b>\n";
            foreach ($items as $item) {
                $importance = str_repeat('⭐', min($item['importance'], 5));
                $memoryText .= "• {$item['key']}: {$item['value']} {$importance}\n";
            }
            $memoryText .= "\n";
        }

        $memoryText .= "💡 Используйте /forget [название] чтобы удалить информацию";

        // Split long messages
        if (strlen($memoryText) > 4000) {
            $chunks = str_split($memoryText, 4000);
            foreach ($chunks as $chunk) {
                $telegramBot->sendMessage($chatId, $chunk);
            }
        } else {
            $telegramBot->sendMessage($chatId, $memoryText);
        }
    }

    /**
     * Shows statistics
     *
     * @param int $chatId
     * @param object $telegramBot
     * @param Conversation $conversation
     * @param int $userId
     */
    private function showStats($chatId, $telegramBot, $conversation, $userId)
    {
        // Message statistics
        $messageCount = Message::find()
            ->where(['conversation_id' => $conversation->id])
            ->count();

        $userMessages = Message::find()
            ->where(['conversation_id' => $conversation->id, 'role' => Message::ROLE_USER])
            ->count();

        $assistantMessages = Message::find()
            ->where(['conversation_id' => $conversation->id, 'role' => Message::ROLE_ASSISTANT])
            ->count();

        $totalTokens = Message::find()
            ->where(['conversation_id' => $conversation->id])
            ->sum('tokens_used');

        // Memory statistics
        $memoryStats = UserMemory::getMemoryStats($userId);

        $statsText = "📊 <b>Статистика</b>\n\n";
        $statsText .= "💬 <b>Сообщения:</b>\n";
        $statsText .= "• Всего сообщений: {$messageCount}\n";
        $statsText .= "• Ваших сообщений: {$userMessages}\n";
        $statsText .= "• Ответов бота: {$assistantMessages}\n";
        $statsText .= "• Использовано токенов: {$totalTokens}\n\n";

        $statsText .= "🧠 <b>Память:</b>\n";
        $statsText .= "• Сохранено фактов: {$memoryStats['total']}\n";

        if (!empty($memoryStats['by_category'])) {
            $categories = UserMemory::getCategories();
            foreach ($memoryStats['by_category'] as $category => $count) {
                $categoryName = $categories[$category] ?? $category;
                $statsText .= "• {$categoryName}: {$count}\n";
            }
        }

        $statsText .= "\n📅 Разговор начат: " . date('d.m.Y H:i', strtotime($conversation->created_at));

        $telegramBot->sendMessage($chatId, $statsText);
    }

    /**
     * Handles forget command
     *
     * @param string $command
     * @param int $chatId
     * @param object $telegramBot
     * @param int $userId
     */
    private function handleForgetCommand($command, $chatId, $telegramBot, $userId)
    {
        $parts = explode(' ', $command, 2);
        
        if (count($parts) < 2) {
            $telegramBot->sendMessage($chatId, '❓ Укажите, что забыть.\n\nПример: /forget имя\n\nИспользуйте /memory чтобы посмотреть сохраненную информацию.');
            return;
        }

        $keyToForget = trim($parts[1]);
        $deleted = UserMemory::deleteMemory($userId, $keyToForget);

        if ($deleted) {
            $telegramBot->sendMessage($chatId, "✅ Забыл информацию: {$keyToForget}");
        } else {
            $telegramBot->sendMessage($chatId, "❌ Не нашел информацию: {$keyToForget}\n\nИспользуйте /memory чтобы посмотреть что я помню о вас.");
        }
    }
}