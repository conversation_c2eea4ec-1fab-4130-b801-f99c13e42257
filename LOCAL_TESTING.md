# 🌐 Локальное тестирование Telegram бота

Это руководство поможет вам настроить локальное тестирование Telegram бота с помощью туннелирования.

## 🚀 Быстрый старт

### 1. Установка ngrok (рекомендуется)

1. **Скачайте ngrok:**
   - Перейдите на https://ngrok.com/download
   - Скачайте версию для Windows
   - Распакуйте в удобную папку (например, `C:\ngrok\`)

2. **Зарегистрируйтесь:**
   - Создайте аккаунт на https://ngrok.com/
   - Получите токен авторизации
   - Выполните: `ngrok authtoken YOUR_TOKEN`

3. **Запустите туннель:**
   ```bash
   ngrok http 80
   ```

4. **Скопируйте HTTPS URL** (например: `https://abc123.ngrok.io`)

### 2. Настройка бота

1. **Настройте webhook:**
   ```bash
   php yii bot/setup-local https://abc123.ngrok.io
   ```

2. **Проверьте компоненты:**
   ```bash
   php yii bot/test-bot
   ```

3. **Начните тестирование** - пишите боту в Telegram!

## 🛠 Альтернативные инструменты

### jprq (простой и быстрый)
```bash
# Установка
pip install jprq

# Запуск
jprq http 80
```

### localtunnel
```bash
# Установка
npm install -g localtunnel

# Запуск
lt --port 80
```

### serveo (без установки)
```bash
ssh -R 80:localhost:80 serveo.net
```

## 📋 Полезные команды

### Управление webhook
```bash
# Информация о webhook
php yii bot/webhook-info

# Удалить webhook
php yii bot/delete-webhook

# Настроить локальный webhook
php yii bot/setup-local https://your-tunnel-url.com
```

### Тестирование компонентов
```bash
# Тест всех компонентов
php yii bot/test-bot

# Тест только Groq API
php yii bot/groq-test

# Тест потокового ответа
php yii bot/stream-test

# Полный тест функций
php yii bot/full-test
```

## 🔧 Настройка конфигурации

### config/params.php
```php
<?php
return [
    'telegramBotToken' => 'YOUR_BOT_TOKEN_FROM_BOTFATHER',
    'groqApiKey' => 'YOUR_GROQ_API_KEY',
    'webhookUrl' => 'https://yourdomain.com/webhook/YOUR_BOT_TOKEN', // Будет обновлен автоматически
    'adminChatId' => 'YOUR_TELEGRAM_USER_ID',
];
```

## 🐛 Отладка

### Проверка логов
```bash
# Логи приложения
tail -f runtime/logs/app.log

# Логи Telegram
tail -f runtime/logs/telegram.log
```

### Проверка webhook
1. Откройте ngrok web interface: http://127.0.0.1:4040
2. Смотрите входящие запросы в реальном времени
3. Проверяйте статус коды и содержимое

### Типичные проблемы

**❌ Webhook не работает:**
- Проверьте, что ngrok запущен
- URL должен быть HTTPS
- Проверьте токен бота

**❌ Groq API не отвечает:**
```bash
php yii bot/groq-test
```

**❌ База данных недоступна:**
- Проверьте config/db.php
- Убедитесь, что сервер БД запущен

## 📱 Создание Telegram бота

1. Найдите @BotFather в Telegram
2. Отправьте `/newbot`
3. Выберите имя и username
4. Скопируйте токен в config/params.php
5. Настройте команды (опционально):
   ```
   /setcommands
   start - Начать работу с ботом
   help - Помощь
   ```

## 🌟 Примеры использования

### Простой диалог
```
Пользователь: Привет!
Бот: Привет! Как дела? Чем могу помочь?

Пользователь: Расскажи анекдот
Бот: [Groq API генерирует анекдот]
```

### Контекстный диалог
```
Пользователь: Меня зовут Иван
Бот: Приятно познакомиться, Иван!

Пользователь: Как меня зовут?
Бот: Вас зовут Иван!
```

## 🚀 Деплой на продакшн

После тестирования можете развернуть на реальном сервере:

1. Загрузите код на сервер
2. Настройте веб-сервер (Apache/Nginx)
3. Обновите webhook URL:
   ```bash
   php yii bot/setup-local https://yourdomain.com
   ```

## 📞 Поддержка

Если возникли проблемы:
1. Проверьте логи
2. Запустите диагностику: `php yii bot/test-bot`
3. Проверьте конфигурацию
4. Убедитесь, что все сервисы запущены