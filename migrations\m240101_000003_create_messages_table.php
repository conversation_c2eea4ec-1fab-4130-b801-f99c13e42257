<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%messages}}`.
 */
class m240101_000003_create_messages_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%messages}}', [
            'id' => $this->primaryKey(),
            'conversation_id' => $this->integer()->notNull(),
            'message_id' => $this->bigInteger()->notNull(),
            'role' => $this->string(20)->notNull(), // 'user' или 'assistant'
            'content' => $this->text()->notNull(),
            'tokens_used' => $this->integer()->defaultValue(0),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
        ]);

        $this->addForeignKey(
            'fk-messages-conversation_id',
            '{{%messages}}',
            'conversation_id',
            '{{%conversations}}',
            'id',
            'CASCADE'
        );

        $this->createIndex('idx-messages-conversation_id', '{{%messages}}', 'conversation_id');
        $this->createIndex('idx-messages-message_id', '{{%messages}}', 'message_id');
        $this->createIndex('idx-messages-created_at', '{{%messages}}', 'created_at');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('fk-messages-conversation_id', '{{%messages}}');
        $this->dropTable('{{%messages}}');
    }
}