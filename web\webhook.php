<?php
// Прямой webhook без Yii фреймворка
header('Content-Type: application/json');

// Получаем токен из URL
$requestUri = $_SERVER['REQUEST_URI'];
$pathParts = explode('/', trim($requestUri, '/'));

// Ожидаем URL вида: /webhook.php/TOKEN
$expectedToken = '8359120434:AAFxfG1bRSKdL5Uf8fdoUfULhtBLeat5GX0';
$providedToken = isset($pathParts[1]) ? $pathParts[1] : '';

// Проверяем токен
if ($providedToken !== $expectedToken) {
    http_response_code(403);
    echo json_encode(['error' => 'Invalid token']);
    exit;
}

// Получаем данные от Telegram
$input = file_get_contents('php://input');
$update = json_decode($input, true);

// Логируем
$logFile = dirname(__DIR__) . '/logs/direct_webhook.log';
$logMessage = date('Y-m-d H:i:s') . " - Webhook получен: " . $input . "\n";
file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);

// Простая обработка сообщения
if (isset($update['message'])) {
    $message = $update['message'];
    $chatId = $message['chat']['id'];
    $text = $message['text'] ?? '';
    
    // Простой ответ
    $responseText = "Получено сообщение: " . $text;
    
    // Отправляем ответ в Telegram
    $telegramUrl = "https://api.telegram.org/bot{$expectedToken}/sendMessage";
    $postData = [
        'chat_id' => $chatId,
        'text' => $responseText
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $telegramUrl);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $result = curl_exec($ch);
    curl_close($ch);
    
    // Логируем ответ
    $logMessage = date('Y-m-d H:i:s') . " - Отправлен ответ: " . $result . "\n";
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

// Возвращаем OK для Telegram
echo json_encode(['ok' => true]);
?>