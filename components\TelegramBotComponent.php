<?php

namespace app\components;

use Yii;
use yii\base\Component;
use <PERSON>uz<PERSON>Http\Client;
use GuzzleHttp\Exception\RequestException;

/**
 * Telegram Bot Component
 */
class TelegramBotComponent extends Component
{
    public $botToken;
    public $webhookUrl;
    public $apiUrl = 'https://api.telegram.org/bot';

    private $client;

    /**
     * {@inheritdoc}
     */
    public function init()
    {
        parent::init();

        if (!$this->botToken) {
            throw new \yii\base\InvalidConfigException('Telegram bot token is required');
        }

        $this->client = new Client([
            'base_uri' => $this->apiUrl . $this->botToken . '/',
            'timeout' => 30,
            'verify' => false, // Отключаем SSL проверку для разработки
        ]);
    }

    /**
     * Sends message to Telegram
     *
     * @param int $chatId
     * @param string $text
     * @param array $options Additional options (reply_markup, parse_mode, etc.)
     * @return array|null
     */
    public function sendMessage($chatId, $text, $options = [])
    {
        $data = array_merge([
            'chat_id' => $chatId,
            'text' => $text,
            'parse_mode' => 'HTML',
        ], $options);

        return $this->makeRequest('sendMessage', $data);
    }

    /**
     * Sends typing action
     *
     * @param int $chatId
     * @return array|null
     */
    public function sendChatAction($chatId, $action = 'typing')
    {
        return $this->makeRequest('sendChatAction', [
            'chat_id' => $chatId,
            'action' => $action,
        ]);
    }

    /**
     * Sets webhook URL
     *
     * @param string $url
     * @param array $options
     * @return array|null
     */
    public function setWebhook($url = null, $options = [])
    {
        $data = array_merge([
            'url' => $url ?: $this->webhookUrl,
        ], $options);

        return $this->makeRequest('setWebhook', $data);
    }

    /**
     * Deletes webhook
     *
     * @return array|null
     */
    public function deleteWebhook()
    {
        return $this->makeRequest('deleteWebhook');
    }

    /**
     * Gets webhook info
     *
     * @return array|null
     */
    public function getWebhookInfo()
    {
        return $this->makeRequest('getWebhookInfo');
    }

    /**
     * Gets bot info
     *
     * @return array|null
     */
    public function getMe()
    {
        return $this->makeRequest('getMe');
    }

    /**
     * Edits message text
     *
     * @param int $chatId
     * @param int $messageId
     * @param string $text
     * @param array $options
     * @return array|null
     */
    public function editMessageText($chatId, $messageId, $text, $options = [])
    {
        $data = array_merge([
            'chat_id' => $chatId,
            'message_id' => $messageId,
            'text' => $text,
            'parse_mode' => 'HTML',
        ], $options);

        return $this->makeRequest('editMessageText', $data);
    }

    /**
     * Deletes message
     *
     * @param int $chatId
     * @param int $messageId
     * @return array|null
     */
    public function deleteMessage($chatId, $messageId)
    {
        return $this->makeRequest('deleteMessage', [
            'chat_id' => $chatId,
            'message_id' => $messageId,
        ]);
    }

    /**
     * Creates inline keyboard
     *
     * @param array $buttons
     * @return array
     */
    public function createInlineKeyboard($buttons)
    {
        return [
            'reply_markup' => [
                'inline_keyboard' => $buttons,
            ],
        ];
    }

    /**
     * Creates reply keyboard
     *
     * @param array $buttons
     * @param bool $oneTime
     * @param bool $resize
     * @return array
     */
    public function createReplyKeyboard($buttons, $oneTime = false, $resize = true)
    {
        return [
            'reply_markup' => [
                'keyboard' => $buttons,
                'one_time_keyboard' => $oneTime,
                'resize_keyboard' => $resize,
            ],
        ];
    }

    /**
     * Removes reply keyboard
     *
     * @return array
     */
    public function removeKeyboard()
    {
        return [
            'reply_markup' => [
                'remove_keyboard' => true,
            ],
        ];
    }

    /**
     * Makes request to Telegram API
     *
     * @param string $method
     * @param array $data
     * @return array|null
     */
    private function makeRequest($method, $data = [])
    {
        $startTime = microtime(true);
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'method' => $method,
            'request_data' => $data,
        ];

        try {
            // Детальное логирование запроса
            $this->logToFile("=== TELEGRAM API REQUEST ===");
            $this->logToFile("Method: $method");
            $this->logToFile("Data: " . json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
            $this->logToFile("Bot Token: " . (empty($this->botToken) ? 'NOT SET' : substr($this->botToken, 0, 10) . '...'));
            $this->logToFile("API URL: " . $this->apiUrl . $this->botToken . '/');

            Yii::info("Telegram API Request: $method - " . json_encode($data), 'telegram');

            $response = $this->client->post($method, [
                'json' => $data,
            ]);

            $responseBody = $response->getBody()->getContents();
            $responseData = json_decode($responseBody, true);
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);

            $logData['response_time_ms'] = $responseTime;
            $logData['response_data'] = $responseData;
            $logData['http_status'] = $response->getStatusCode();

            // Детальное логирование ответа
            $this->logToFile("=== TELEGRAM API RESPONSE ===");
            $this->logToFile("HTTP Status: " . $response->getStatusCode());
            $this->logToFile("Response Time: {$responseTime}ms");
            $this->logToFile("Response Body: " . $responseBody);

            Yii::info("Telegram API Response: $method - " . json_encode($responseData), 'telegram');

            if (!$responseData || !isset($responseData['ok'])) {
                $this->logToFile("ERROR: Invalid response format");
                $this->logToFile("Raw response: " . $responseBody);
                Yii::error("Telegram API Invalid Response: $method - " . $responseBody, 'telegram');
                return null;
            }

            if (!$responseData['ok']) {
                $errorCode = $responseData['error_code'] ?? 'unknown';
                $errorDescription = $responseData['description'] ?? 'No description';
                
                $this->logToFile("ERROR: API returned error");
                $this->logToFile("Error Code: $errorCode");
                $this->logToFile("Error Description: $errorDescription");
                
                Yii::error("Telegram API Error: $method - Code: $errorCode, Description: $errorDescription", 'telegram');
                return null;
            }

            $this->logToFile("SUCCESS: Request completed successfully");
            $this->logToFile("Result: " . json_encode($responseData['result'], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
            $this->logToFile(""); // Empty line for separation

            return $responseData['result'];
        } catch (RequestException $e) {
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);
            $errorMessage = "Telegram API Request Error: $method - " . $e->getMessage();
            $errorBody = '';

            if ($e->hasResponse()) {
                $errorBody = $e->getResponse()->getBody()->getContents();
                $errorMessage .= ' Response: ' . $errorBody;
                $logData['http_status'] = $e->getResponse()->getStatusCode();
            }

            $logData['response_time_ms'] = $responseTime;
            $logData['error'] = $errorMessage;
            $logData['error_body'] = $errorBody;

            $this->logToFile("=== TELEGRAM API REQUEST EXCEPTION ===");
            $this->logToFile("Error: " . $e->getMessage());
            $this->logToFile("Response Time: {$responseTime}ms");
            if ($errorBody) {
                $this->logToFile("Error Response: " . $errorBody);
            }
            $this->logToFile(""); // Empty line for separation

            Yii::error($errorMessage, 'telegram');
            return null;
        } catch (\Exception $e) {
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);
            $errorMessage = "Telegram API Exception: $method - " . $e->getMessage();
            
            $logData['response_time_ms'] = $responseTime;
            $logData['exception'] = $errorMessage;
            $logData['file'] = $e->getFile();
            $logData['line'] = $e->getLine();

            $this->logToFile("=== TELEGRAM API GENERAL EXCEPTION ===");
            $this->logToFile("Exception: " . $e->getMessage());
            $this->logToFile("File: " . $e->getFile() . ":" . $e->getLine());
            $this->logToFile("Response Time: {$responseTime}ms");
            $this->logToFile(""); // Empty line for separation

            Yii::error($errorMessage, 'telegram');
            return null;
        }
    }

    /**
     * Logs message to bot-specific log file
     *
     * @param string $message
     */
    private function logToFile($message)
    {
        $logFile = Yii::getAlias('@app/logs/bot.log');
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] $message" . PHP_EOL;
        
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
}
