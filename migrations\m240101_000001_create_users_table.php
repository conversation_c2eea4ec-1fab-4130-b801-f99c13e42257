<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%users}}`.
 */
class m240101_000001_create_users_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%users}}', [
            'id' => $this->primaryKey(),
            'telegram_id' => $this->bigInteger()->notNull()->unique(),
            'username' => $this->string(255),
            'first_name' => $this->string(255),
            'last_name' => $this->string(255),
            'language_code' => $this->string(10),
            'is_bot' => $this->boolean()->defaultValue(false),
            'is_premium' => $this->boolean()->defaultValue(false),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'updated_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
        ]);

        $this->createIndex('idx-users-telegram_id', '{{%users}}', 'telegram_id');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%users}}');
    }
}