# 🚀 Быстрая настройка для тестирования

## 1. 🤖 Создание Telegram бота

1. **Найдите @BotFather в Telegram**
2. **Отправьте команду:** `/newbot`
3. **Выберите имя бота:** например "My Groq Bot"
4. **Выберите username:** например "my_groq_test_bot"
5. **Скопируйте токен** (выглядит как: `1234567890:ABCdefGHIjklMNOpqrsTUVwxyz`)

## 2. ⚙️ Настройка конфигурации

Откройте файл `config/params.php` и замените:

```php
<?php
return [
    'telegramBotToken' => 'ВСТАВЬТЕ_ТОКЕН_БОТА_СЮДА',
    'groqApiKey' => '********************************************************',
    'webhookUrl' => 'https://yourdomain.com/webhook/ВСТАВЬТЕ_ТОКЕН_БОТА_СЮДА',
    'adminChatId' => 'YOUR_ADMIN_CHAT_ID',
];
```

## 3. 🌐 Настройка туннелирования

### Вариант A: ngrok (рекомендуется)
1. **Скачайте:** https://ngrok.com/download
2. **Запустите:** `ngrok http 80`
3. **Скопируйте HTTPS URL:** например `https://abc123.ngrok.io`

### Вариант B: jprq (проще)
1. **Установите:** `pip install jprq`
2. **Запустите:** `jprq http 80`
3. **Скопируйте URL**

## 4. 🔧 Запуск тестирования

```bash
# Проверьте компоненты
php yii bot/test-bot

# Настройте webhook (замените URL на свой)
php yii bot/setup-local https://abc123.ngrok.io

# Проверьте webhook
php yii bot/webhook-info
```

## 5. 📱 Тестирование

1. **Найдите своего бота в Telegram** (по username)
2. **Отправьте:** `/start`
3. **Попробуйте:** "Привет, как дела?"

## 🐛 Если что-то не работает

### Telegram API не работает:
```bash
# Проверьте токен в config/params.php
php yii bot/test-bot
```

### Groq API не работает:
```bash
# Проверьте API ключ
php yii bot/groq-test
```

### Webhook не работает:
```bash
# Проверьте информацию
php yii bot/webhook-info

# Удалите и пересоздайте
php yii bot/delete-webhook
php yii bot/setup-local https://your-url.ngrok.io
```

### База данных не работает:
- Для тестирования можно пока игнорировать
- Бот будет работать без сохранения истории

## 📋 Полезные команды

```bash
# Тестирование
php yii bot/test-bot          # Тест всех компонентов
php yii bot/groq-test         # Тест только Groq API
php yii bot/webhook-info      # Информация о webhook

# Управление webhook
php yii bot/setup-local <url> # Настроить локальный webhook
php yii bot/delete-webhook    # Удалить webhook

# Отладка
tail -f runtime/logs/app.log      # Логи приложения
tail -f runtime/logs/telegram.log # Логи Telegram
```

## 🎯 Минимальная настройка для тестирования

Если хотите быстро протестировать только Groq API:

1. **Настройте токен бота** в `config/params.php`
2. **Запустите ngrok:** `ngrok http 80`
3. **Настройте webhook:** `php yii bot/setup-local https://your-url.ngrok.io`
4. **Тестируйте в Telegram!**

База данных не обязательна для базового тестирования.