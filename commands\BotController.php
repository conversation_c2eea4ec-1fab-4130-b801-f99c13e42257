<?php

namespace app\commands;

use Yii;
use yii\console\Controller;
use yii\console\ExitCode;

/**
 * Bot management commands
 */
class BotController extends Controller
{
    /**
     * Test Groq API using component methods
     * @return int
     */
    public function actionGroqTest()
    {
        $this->stdout("🧠 Тестирование Groq API через компонент...\n\n", \yii\helpers\Console::FG_BLUE);

        try {
            $groqApi = Yii::$app->groqApi;

            $this->stdout("📋 Конфигурация:\n");
            $this->stdout("  • API Key: " . (empty($groqApi->apiKey) ? "❌ НЕ УСТАНОВЛЕН" : "✅ Установлен (" . substr($groqApi->apiKey, 0, 10) . "...)") . "\n");
            $this->stdout("  • Base URL: " . $groqApi->baseUrl . "\n");
            $this->stdout("  • Model: " . $groqApi->model . "\n\n");

            // Test 1: Simple message
            $this->stdout("1️⃣ Тестирование простого сообщения...\n", \yii\helpers\Console::FG_YELLOW);
            $testMessage = 'Скажи "Привет, мир!" на русском языке.';
            $this->stdout("Отправляем: \"{$testMessage}\"\n");

            $startTime = microtime(true);
            $response = $groqApi->generateResponse($testMessage);
            $endTime = microtime(true);
            $responseTime = round(($endTime - $startTime) * 1000, 2);

            // Детальная диагностика
            $this->stdout("Debug: Response type: " . gettype($response) . "\n");
            if (is_array($response)) {
                $this->stdout("Debug: Response keys: " . implode(', ', array_keys($response)) . "\n");
            }

            if ($response) {
                $this->stdout("✅ Ответ получен за {$responseTime}мс\n", \yii\helpers\Console::FG_GREEN);

                $responseText = $groqApi->extractResponseText($response);
                $usage = $groqApi->getTokenUsage($response);

                $this->stdout("\n📝 Ответ от AI:\n", \yii\helpers\Console::FG_CYAN);
                $this->stdout("─────────────────────────────────────\n");
                $this->stdout($responseText . "\n");
                $this->stdout("─────────────────────────────────────\n\n");

                $this->stdout("📊 Статистика токенов:\n", \yii\helpers\Console::FG_BLUE);
                $this->stdout("  • Токены запроса: " . $usage['prompt_tokens'] . "\n");
                $this->stdout("  • Токены ответа: " . $usage['completion_tokens'] . "\n");
                $this->stdout("  • Всего токенов: " . $usage['total_tokens'] . "\n");

                if (isset($response['model'])) {
                    $this->stdout("  • Использованная модель: " . $response['model'] . "\n");
                }
            } else {
                $this->stderr("❌ Не удалось получить ответ от API\n", \yii\helpers\Console::FG_RED);
                return \yii\console\ExitCode::UNSPECIFIED_ERROR;
            }

            $this->stdout("\n");

            // Test 2: Context conversation
            $this->stdout("2️⃣ Тестирование контекстного диалога...\n", \yii\helpers\Console::FG_YELLOW);

            $context = [
                ['role' => 'user', 'content' => 'Меня зовут Алексей, я программист из России'],
                ['role' => 'assistant', 'content' => 'Приятно познакомиться, Алексей! Здорово, что вы программист из России.'],
            ];

            $contextMessage = 'Как меня зовут и откуда я?';
            $this->stdout("Отправляем с контекстом: \"{$contextMessage}\"\n");

            $contextResponse = $groqApi->generateResponse($contextMessage, $context);

            if ($contextResponse) {
                $contextText = $groqApi->extractResponseText($contextResponse);
                $this->stdout("✅ Контекстный ответ:\n", \yii\helpers\Console::FG_GREEN);
                $this->stdout("─────────────────────────────────────\n");
                $this->stdout($contextText . "\n");
                $this->stdout("─────────────────────────────────────\n");
            } else {
                $this->stderr("❌ Не удалось получить контекстный ответ\n", \yii\helpers\Console::FG_RED);
            }

            $this->stdout("\n");

            // Test 3: Different parameters
            $this->stdout("3️⃣ Тестирование с разными параметрами...\n", \yii\helpers\Console::FG_YELLOW);

            $creativityTest = 'Придумай короткую историю про кота.';
            $this->stdout("Отправляем креативный запрос: \"{$creativityTest}\"\n");

            $creativeResponse = $groqApi->generateResponse($creativityTest, [], [
                'temperature' => 0.9,
                'max_tokens' => 150
            ]);

            if ($creativeResponse) {
                $creativeText = $groqApi->extractResponseText($creativeResponse);
                $this->stdout("✅ Креативный ответ:\n", \yii\helpers\Console::FG_GREEN);
                $this->stdout("─────────────────────────────────────\n");
                $this->stdout($creativeText . "\n");
                $this->stdout("─────────────────────────────────────\n");

                $creativeUsage = $groqApi->getTokenUsage($creativeResponse);
                $this->stdout("📊 Токены: " . $creativeUsage['total_tokens'] . "\n");
            } else {
                $this->stderr("❌ Не удалось получить креативный ответ\n", \yii\helpers\Console::FG_RED);
            }

            $this->stdout("\n");

            // Test 4: Technical question
            $this->stdout("4️⃣ Тестирование технического вопроса...\n", \yii\helpers\Console::FG_YELLOW);

            $techQuestion = 'Объясни что такое REST API простыми словами.';
            $this->stdout("Отправляем технический вопрос: \"{$techQuestion}\"\n");

            $techResponse = $groqApi->generateResponse($techQuestion, [], [
                'temperature' => 0.3,
                'max_tokens' => 200
            ]);

            if ($techResponse) {
                $techText = $groqApi->extractResponseText($techResponse);
                $this->stdout("✅ Технический ответ:\n", \yii\helpers\Console::FG_GREEN);
                $this->stdout("─────────────────────────────────────\n");
                $this->stdout($techText . "\n");
                $this->stdout("─────────────────────────────────────\n");
            } else {
                $this->stderr("❌ Не удалось получить технический ответ\n", \yii\helpers\Console::FG_RED);
            }
        } catch (\Exception $e) {
            $this->stderr("❌ Ошибка: " . $e->getMessage() . "\n", \yii\helpers\Console::FG_RED);
            $this->stderr("Файл: " . $e->getFile() . ":" . $e->getLine() . "\n", \yii\helpers\Console::FG_RED);
            return \yii\console\ExitCode::UNSPECIFIED_ERROR;
        }

        $this->stdout("\n🎉 Все тесты завершены успешно! Groq API работает корректно.\n", \yii\helpers\Console::FG_GREEN);
        return \yii\console\ExitCode::OK;
    }

    /**
     * Test models endpoint
     * @return int
     */
    public function actionTestModels()
    {
        $this->stdout("📋 Тестирование получения списка моделей...\n\n", \yii\helpers\Console::FG_BLUE);

        try {
            $groqApi = Yii::$app->groqApi;
            $models = $groqApi->getModels();

            if ($models && isset($models['data'])) {
                $this->stdout("✅ Получено моделей: " . count($models['data']) . "\n", \yii\helpers\Console::FG_GREEN);
                $this->stdout("📋 Доступные модели:\n");
                foreach ($models['data'] as $model) {
                    $this->stdout("  • " . $model['id'] . "\n");
                }
            } else {
                $this->stderr("❌ Не удалось получить список моделей\n", \yii\helpers\Console::FG_RED);
                return \yii\console\ExitCode::UNSPECIFIED_ERROR;
            }
        } catch (\Exception $e) {
            $this->stderr("❌ Ошибка: " . $e->getMessage() . "\n", \yii\helpers\Console::FG_RED);
            return \yii\console\ExitCode::UNSPECIFIED_ERROR;
        }

        return \yii\console\ExitCode::OK;
    }

    /**
     * Direct chat completion test
     * @return int
     */
    public function actionDirectTest()
    {
        $this->stdout("🔧 Прямой тест chatCompletion...\n\n", \yii\helpers\Console::FG_BLUE);

        try {
            $groqApi = Yii::$app->groqApi;

            $messages = [
                ['role' => 'user', 'content' => 'Скажи привет']
            ];

            $this->stdout("Отправляем прямой запрос...\n");
            $response = $groqApi->chatCompletion($messages);

            $this->stdout("Response type: " . gettype($response) . "\n");

            if ($response === null) {
                $this->stderr("❌ Получен null ответ\n", \yii\helpers\Console::FG_RED);

                // Проверим логи
                $logFile = Yii::getAlias('@runtime/logs/app.log');
                if (file_exists($logFile)) {
                    $this->stdout("📋 Последние строки лога:\n");
                    $logContent = file_get_contents($logFile);
                    $lines = explode("\n", $logContent);
                    $lastLines = array_slice($lines, -10);
                    foreach ($lastLines as $line) {
                        if (strpos($line, 'groq') !== false || strpos($line, 'error') !== false) {
                            $this->stdout($line . "\n");
                        }
                    }
                }

                return \yii\console\ExitCode::UNSPECIFIED_ERROR;
            }

            if (is_array($response)) {
                $this->stdout("✅ Получен массив ответа\n", \yii\helpers\Console::FG_GREEN);
                $this->stdout("Keys: " . implode(', ', array_keys($response)) . "\n");

                if (isset($response['choices'])) {
                    $this->stdout("✅ Есть choices\n", \yii\helpers\Console::FG_GREEN);
                    if (isset($response['choices'][0]['message']['content'])) {
                        $content = $response['choices'][0]['message']['content'];
                        $this->stdout("✅ Ответ: {$content}\n", \yii\helpers\Console::FG_GREEN);
                    }
                }

                if (isset($response['usage'])) {
                    $usage = $response['usage'];
                    $this->stdout("📊 Токены: {$usage['total_tokens']}\n");
                }
            }
        } catch (\Exception $e) {
            $this->stderr("❌ Exception: " . $e->getMessage() . "\n", \yii\helpers\Console::FG_RED);
            $this->stderr("File: " . $e->getFile() . ":" . $e->getLine() . "\n", \yii\helpers\Console::FG_RED);
            return \yii\console\ExitCode::UNSPECIFIED_ERROR;
        }

        return ExitCode::OK;
    }

    /**
     * Test streaming response
     * @return int
     */
    public function actionStreamTest()
    {
        $this->stdout("🌊 Тестирование потокового ответа...\n\n", \yii\helpers\Console::FG_BLUE);

        try {
            $groqApi = Yii::$app->groqApi;

            $messages = [
                ['role' => 'user', 'content' => 'Расскажи короткую историю про робота']
            ];

            $this->stdout("📝 Потоковый ответ:\n", \yii\helpers\Console::FG_CYAN);
            $this->stdout("─────────────────────────────────────\n");

            $success = $groqApi->streamChatCompletion($messages, function ($chunk) {
                $this->stdout($chunk, \yii\helpers\Console::FG_GREEN);
            });

            $this->stdout("\n─────────────────────────────────────\n");

            if ($success) {
                $this->stdout("✅ Потоковый ответ получен успешно\n", \yii\helpers\Console::FG_GREEN);
            } else {
                $this->stderr("❌ Ошибка при получении потокового ответа\n", \yii\helpers\Console::FG_RED);
                return ExitCode::UNSPECIFIED_ERROR;
            }
        } catch (\Exception $e) {
            $this->stderr("❌ Ошибка: " . $e->getMessage() . "\n", \yii\helpers\Console::FG_RED);
            return ExitCode::UNSPECIFIED_ERROR;
        }

        return ExitCode::OK;
    }

    /**
     * Show available models
     * @return int  
     */
    public function actionShowModels()
    {
        $this->stdout("🤖 Доступные модели Groq:\n\n", \yii\helpers\Console::FG_BLUE);

        try {
            $groqApi = Yii::$app->groqApi;
            $models = $groqApi->getAvailableModels();

            foreach ($models as $id => $info) {
                $this->stdout("📋 {$info['name']} ({$id})\n", \yii\helpers\Console::FG_CYAN);
                $this->stdout("   Context: {$info['context_window']} tokens\n");
                $this->stdout("   {$info['description']}\n\n");
            }
        } catch (\Exception $e) {
            $this->stderr("❌ Ошибка: " . $e->getMessage() . "\n", \yii\helpers\Console::FG_RED);
            return ExitCode::UNSPECIFIED_ERROR;
        }

        return ExitCode::OK;
    }

    

   

 


  

    /**
     * Тест всех компонентов бота
     * @return int
     */
    public function actionTestBot()
    {
        $this->stdout("🤖 Тестирование всех компонентов бота...\n\n", \yii\helpers\Console::FG_BLUE);
        
        try {
            // 1. Тест Telegram API
            $this->stdout("1️⃣ Тестирование Telegram Bot API...\n", \yii\helpers\Console::FG_YELLOW);
            $telegramBot = Yii::$app->telegramBot;
            $botInfo = $telegramBot->getMe();
            
            if ($botInfo) {
                $this->stdout("✅ Telegram API: OK\n", \yii\helpers\Console::FG_GREEN);
                $this->stdout("  • Bot Username: @" . $botInfo['username'] . "\n");
                $this->stdout("  • Bot Name: " . $botInfo['first_name'] . "\n");
            } else {
                $this->stderr("❌ Telegram API: FAILED\n", \yii\helpers\Console::FG_RED);
            }
            
            $this->stdout("\n");
            
            // 2. Тест Groq API
            $this->stdout("2️⃣ Тестирование Groq API...\n", \yii\helpers\Console::FG_YELLOW);
            $groqApi = Yii::$app->groqApi;
            $response = $groqApi->generateResponse('Привет! Это тест.');
            
            if ($response) {
                $this->stdout("✅ Groq API: OK\n", \yii\helpers\Console::FG_GREEN);
                $responseText = $groqApi->extractResponseText($response);
                $this->stdout("  • Test Response: " . substr($responseText, 0, 50) . "...\n");
                
                $usage = $groqApi->getTokenUsage($response);
                $this->stdout("  • Tokens Used: " . $usage['total_tokens'] . "\n");
            } else {
                $this->stderr("❌ Groq API: FAILED\n", \yii\helpers\Console::FG_RED);
            }
            
            $this->stdout("\n");
            
            // 3. Тест базы данных
            $this->stdout("3️⃣ Тестирование базы данных...\n", \yii\helpers\Console::FG_YELLOW);
            try {
                $db = Yii::$app->db;
                $db->open();
                $this->stdout("✅ Database: OK\n", \yii\helpers\Console::FG_GREEN);
                $this->stdout("  • DSN: " . $db->dsn . "\n");
            } catch (\Exception $e) {
                $this->stderr("❌ Database: FAILED - " . $e->getMessage() . "\n", \yii\helpers\Console::FG_RED);
            }
            
            $this->stdout("\n");
            
            // 4. Тест webhook
            $this->stdout("4️⃣ Тестирование webhook...\n", \yii\helpers\Console::FG_YELLOW);
            $webhookInfo = $telegramBot->getWebhookInfo();
            
            if ($webhookInfo && !empty($webhookInfo['url'])) {
                $this->stdout("✅ Webhook: OK\n", \yii\helpers\Console::FG_GREEN);
                $this->stdout("  • URL: " . $webhookInfo['url'] . "\n");
                $this->stdout("  • Pending: " . $webhookInfo['pending_update_count'] . "\n");
            } else {
                $this->stdout("⚠️ Webhook: Not set\n", \yii\helpers\Console::FG_YELLOW);
                $this->stdout("  • Используйте: php yii bot/setup-local <ngrok-url>\n");
            }
            
        } catch (\Exception $e) {
            $this->stderr("❌ Ошибка: " . $e->getMessage() . "\n", \yii\helpers\Console::FG_RED);
            return ExitCode::UNSPECIFIED_ERROR;
        }
        
        $this->stdout("\n🎉 Тестирование завершено!\n", \yii\helpers\Console::FG_GREEN);
        return ExitCode::OK;
    }

    /**
     * Получить информацию о webhook
     * @return int
     */
    public function actionWebhookInfo()
    {
        $this->stdout("📡 Информация о webhook...\n\n", \yii\helpers\Console::FG_BLUE);
        
        try {
            $telegramBot = Yii::$app->telegramBot;
            $result = $telegramBot->getWebhookInfo();
            
            if ($result) {
                $this->stdout("📋 Webhook Information:\n", \yii\helpers\Console::FG_CYAN);
                $this->stdout("  • URL: " . ($result['url'] ?: 'Не установлен') . "\n");
                $this->stdout("  • Has Custom Certificate: " . ($result['has_custom_certificate'] ? 'Yes' : 'No') . "\n");
                $this->stdout("  • Pending Update Count: " . $result['pending_update_count'] . "\n");
                
                if (isset($result['last_error_date'])) {
                    $this->stdout("  • Last Error Date: " . date('Y-m-d H:i:s', $result['last_error_date']) . "\n");
                    $this->stdout("  • Last Error Message: " . $result['last_error_message'] . "\n");
                } else {
                    $this->stdout("  • Ошибок нет ✅\n");
                }
                
                if (isset($result['max_connections'])) {
                    $this->stdout("  • Max Connections: " . $result['max_connections'] . "\n");
                }
                
                if (isset($result['allowed_updates'])) {
                    $this->stdout("  • Allowed Updates: " . implode(', ', $result['allowed_updates']) . "\n");
                }
                
            } else {
                $this->stderr("❌ Не удалось получить информацию о webhook\n", \yii\helpers\Console::FG_RED);
                return ExitCode::UNSPECIFIED_ERROR;
            }
            
        } catch (\Exception $e) {
            $this->stderr("❌ Ошибка: " . $e->getMessage() . "\n", \yii\helpers\Console::FG_RED);
            return ExitCode::UNSPECIFIED_ERROR;
        }
        
        return ExitCode::OK;
    }

    /**
     * Удалить webhook
     * @return int
     */
    public function actionDeleteWebhook()
    {
        $this->stdout("🗑️ Удаление webhook...\n\n", \yii\helpers\Console::FG_BLUE);
        
        try {
            $telegramBot = Yii::$app->telegramBot;
            $result = $telegramBot->deleteWebhook();
            
            if ($result) {
                $this->stdout("✅ Webhook удален успешно!\n", \yii\helpers\Console::FG_GREEN);
                $this->stdout("📱 Бот переведен в режим polling\n");
            } else {
                $this->stderr("❌ Не удалось удалить webhook\n", \yii\helpers\Console::FG_RED);
                return ExitCode::UNSPECIFIED_ERROR;
            }
            
        } catch (\Exception $e) {
            $this->stderr("❌ Ошибка: " . $e->getMessage() . "\n", \yii\helpers\Console::FG_RED);
            return ExitCode::UNSPECIFIED_ERROR;
        }
        
        return ExitCode::OK;
    }

    /**
     * Тест отправки сообщения боту с детальным логированием
     * @param string $chatId ID чата для тестирования
     * @param string $message Сообщение для отправки (по умолчанию "Привет!")
     * @return int
     */
    public function actionTestMessage($chatId = null, $message = 'Привет!')
    {
        if (!$chatId) {
            $this->stderr("❌ Укажите chat_id для тестирования\n", \yii\helpers\Console::FG_RED);
            $this->stdout("Пример: php yii bot/test-message 123456789 \"Привет, бот!\"\n");
            return ExitCode::UNSPECIFIED_ERROR;
        }

        $this->stdout("🧪 Тестирование отправки сообщения боту...\n\n", \yii\helpers\Console::FG_BLUE);
        $this->stdout("Chat ID: $chatId\n");
        $this->stdout("Message: $message\n\n");

        try {
            // Очистим лог перед тестом
            $this->clearBotLog();

            $telegramBot = Yii::$app->telegramBot;
            $groqApi = Yii::$app->groqApi;

            // Симулируем входящее сообщение
            $update = [
                'message' => [
                    'message_id' => time(),
                    'chat' => [
                        'id' => (int)$chatId,
                        'type' => 'private'
                    ],
                    'from' => [
                        'id' => (int)$chatId,
                        'first_name' => 'Test',
                        'username' => 'testuser'
                    ],
                    'text' => $message,
                    'date' => time()
                ]
            ];

            $this->stdout("1️⃣ Проверка компонентов...\n", \yii\helpers\Console::FG_YELLOW);
            
            // Проверка Telegram API
            $botInfo = $telegramBot->getMe();
            if ($botInfo) {
                $this->stdout("✅ Telegram API: OK (@" . $botInfo['username'] . ")\n", \yii\helpers\Console::FG_GREEN);
            } else {
                $this->stderr("❌ Telegram API: FAILED\n", \yii\helpers\Console::FG_RED);
                return ExitCode::UNSPECIFIED_ERROR;
            }

            // Проверка Groq API
            $testResponse = $groqApi->generateResponse('Test');
            if ($testResponse) {
                $this->stdout("✅ Groq API: OK\n", \yii\helpers\Console::FG_GREEN);
            } else {
                $this->stderr("❌ Groq API: FAILED\n", \yii\helpers\Console::FG_RED);
                return ExitCode::UNSPECIFIED_ERROR;
            }

            $this->stdout("\n2️⃣ Симуляция обработки сообщения...\n", \yii\helpers\Console::FG_YELLOW);

            // Создаем контроллер и обрабатываем сообщение
            $controller = new \app\controllers\TelegramController('telegram', Yii::$app);
            
            // Используем рефлексию для вызова приватного метода
            $reflection = new \ReflectionClass($controller);
            $method = $reflection->getMethod('processMessage');
            $method->setAccessible(true);
            
            $startTime = microtime(true);
            $method->invoke($controller, $update['message']);
            $processingTime = round((microtime(true) - $startTime) * 1000, 2);

            $this->stdout("✅ Сообщение обработано за {$processingTime}ms\n", \yii\helpers\Console::FG_GREEN);

            $this->stdout("\n3️⃣ Анализ логов...\n", \yii\helpers\Console::FG_YELLOW);
            $this->showBotLog();

        } catch (\Exception $e) {
            $this->stderr("❌ Ошибка: " . $e->getMessage() . "\n", \yii\helpers\Console::FG_RED);
            $this->stderr("Файл: " . $e->getFile() . ":" . $e->getLine() . "\n", \yii\helpers\Console::FG_RED);
            
            $this->stdout("\n📋 Логи для диагностики:\n", \yii\helpers\Console::FG_YELLOW);
            $this->showBotLog();
            
            return ExitCode::UNSPECIFIED_ERROR;
        }

        return ExitCode::OK;
    }

    /**
     * Показать логи бота
     * @param int $lines Количество последних строк для показа
     * @return int
     */
    public function actionShowLogs($lines = 50)
    {
        $this->stdout("📋 Последние $lines строк лога бота...\n\n", \yii\helpers\Console::FG_BLUE);
        
        $this->showBotLog($lines);
        
        return ExitCode::OK;
    }

    /**
     * Очистить логи бота
     * @return int
     */
    public function actionClearLogs()
    {
        $this->stdout("🗑️ Очистка логов бота...\n", \yii\helpers\Console::FG_BLUE);
        
        $cleared = $this->clearBotLog();
        
        if ($cleared) {
            $this->stdout("✅ Логи очищены успешно!\n", \yii\helpers\Console::FG_GREEN);
        } else {
            $this->stdout("⚠️ Файл лога не существует или уже пуст\n", \yii\helpers\Console::FG_YELLOW);
        }
        
        return ExitCode::OK;
    }

    /**
     * Мониторинг логов в реальном времени
     * @return int
     */
    public function actionTailLogs()
    {
        $this->stdout("👁️ Мониторинг логов бота (Ctrl+C для выхода)...\n\n", \yii\helpers\Console::FG_BLUE);
        
        $logFile = Yii::getAlias('@app/logs/bot.log');
        
        if (!file_exists($logFile)) {
            $this->stdout("⚠️ Файл лога не существует. Ожидание создания...\n", \yii\helpers\Console::FG_YELLOW);
        }
        
        $lastSize = file_exists($logFile) ? filesize($logFile) : 0;
        
        while (true) {
            if (file_exists($logFile)) {
                $currentSize = filesize($logFile);
                
                if ($currentSize > $lastSize) {
                    $handle = fopen($logFile, 'r');
                    fseek($handle, $lastSize);
                    
                    while (($line = fgets($handle)) !== false) {
                        $this->stdout($line);
                    }
                    
                    fclose($handle);
                    $lastSize = $currentSize;
                }
            }
            
            usleep(500000); // 0.5 секунды
        }
        
        return ExitCode::OK;
    }

    /**
     * Показать содержимое лога бота
     * @param int $lines
     */
    private function showBotLog($lines = 50)
    {
        $logFile = Yii::getAlias('@app/logs/bot.log');
        
        if (!file_exists($logFile)) {
            $this->stdout("⚠️ Файл лога не существует: $logFile\n", \yii\helpers\Console::FG_YELLOW);
            return;
        }
        
        $content = file_get_contents($logFile);
        if (empty($content)) {
            $this->stdout("⚠️ Файл лога пуст\n", \yii\helpers\Console::FG_YELLOW);
            return;
        }
        
        $logLines = explode("\n", $content);
        $logLines = array_filter($logLines); // Убираем пустые строки
        
        if ($lines > 0) {
            $logLines = array_slice($logLines, -$lines);
        }
        
        foreach ($logLines as $line) {
            // Цветовое выделение разных типов сообщений
            if (strpos($line, 'ERROR:') !== false || strpos($line, 'EXCEPTION') !== false) {
                $this->stdout($line . "\n", \yii\helpers\Console::FG_RED);
            } elseif (strpos($line, 'SUCCESS:') !== false) {
                $this->stdout($line . "\n", \yii\helpers\Console::FG_GREEN);
            } elseif (strpos($line, '===') !== false) {
                $this->stdout($line . "\n", \yii\helpers\Console::FG_CYAN);
            } else {
                $this->stdout($line . "\n");
            }
        }
        
        $this->stdout("\n📊 Всего строк в логе: " . count(explode("\n", $content)) . "\n", \yii\helpers\Console::FG_BLUE);
        $this->stdout("📁 Путь к файлу: $logFile\n", \yii\helpers\Console::FG_BLUE);
        $this->stdout("📏 Размер файла: " . round(filesize($logFile) / 1024, 2) . " KB\n", \yii\helpers\Console::FG_BLUE);
    }

    /**
     * Очистить лог бота
     * @return bool
     */
    private function clearBotLog()
    {
        $logFile = Yii::getAlias('@app/logs/bot.log');
        
        if (file_exists($logFile)) {
            return file_put_contents($logFile, '') !== false;
        }
        
        return false;
    }

    /**
     * Тест webhook endpoint
     * @return int
     */
    public function actionTestWebhook()
    {
        $this->stdout("🔗 Тестирование webhook endpoint...\n\n", \yii\helpers\Console::FG_BLUE);
        
        $webhookUrl = Yii::$app->params['webhookUrl'] ?? null;
        $botToken = Yii::$app->params['telegramBotToken'] ?? null;
        
        if (!$webhookUrl) {
            $this->stderr("❌ Webhook URL не установлен в config/params.php\n", \yii\helpers\Console::FG_RED);
            return ExitCode::UNSPECIFIED_ERROR;
        }
        
        if (!$botToken) {
            $this->stderr("❌ Bot Token не установлен в config/params.php\n", \yii\helpers\Console::FG_RED);
            return ExitCode::UNSPECIFIED_ERROR;
        }
        
        $this->stdout("Webhook URL: $webhookUrl\n");
        
        // Создаем тестовое сообщение
        $testUpdate = [
            'update_id' => 12345,
            'message' => [
                'message_id' => 1,
                'from' => [
                    'id' => 123456789,
                    'is_bot' => false,
                    'first_name' => 'Test',
                    'username' => 'testuser',
                    'language_code' => 'ru'
                ],
                'chat' => [
                    'id' => 123456789,
                    'first_name' => 'Test',
                    'username' => 'testuser',
                    'type' => 'private'
                ],
                'date' => time(),
                'text' => 'Тест webhook'
            ]
        ];
        
        try {
            $this->stdout("📤 Отправка тестового запроса...\n", \yii\helpers\Console::FG_YELLOW);
            
            $client = new \GuzzleHttp\Client([
                'timeout' => 10,
                'verify' => false
            ]);
            
            $response = $client->post($webhookUrl, [
                'json' => $testUpdate,
                'headers' => [
                    'Content-Type' => 'application/json'
                ]
            ]);
            
            $statusCode = $response->getStatusCode();
            $responseBody = $response->getBody()->getContents();
            
            $this->stdout("📥 Ответ получен:\n", \yii\helpers\Console::FG_GREEN);
            $this->stdout("HTTP Status: $statusCode\n");
            $this->stdout("Response: $responseBody\n");
            
            if ($statusCode === 200) {
                $this->stdout("✅ Webhook работает корректно!\n", \yii\helpers\Console::FG_GREEN);
                
                // Проверим логи
                $this->stdout("\n📋 Последние записи в логе:\n", \yii\helpers\Console::FG_CYAN);
                $this->showBotLog(10);
                
                return ExitCode::OK;
            } else {
                $this->stderr("❌ Webhook вернул неожиданный статус: $statusCode\n", \yii\helpers\Console::FG_RED);
                return ExitCode::UNSPECIFIED_ERROR;
            }
            
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            $this->stderr("❌ Ошибка HTTP запроса: " . $e->getMessage() . "\n", \yii\helpers\Console::FG_RED);
            
            if ($e->hasResponse()) {
                $statusCode = $e->getResponse()->getStatusCode();
                $responseBody = $e->getResponse()->getBody()->getContents();
                $this->stderr("HTTP Status: $statusCode\n", \yii\helpers\Console::FG_RED);
                $this->stderr("Response: $responseBody\n", \yii\helpers\Console::FG_RED);
            }
            
            return ExitCode::UNSPECIFIED_ERROR;
        } catch (\Exception $e) {
            $this->stderr("❌ Общая ошибка: " . $e->getMessage() . "\n", \yii\helpers\Console::FG_RED);
            return ExitCode::UNSPECIFIED_ERROR;
        }
    }

    /**
     * Установить webhook с новым URL
     * @param string $ngrokUrl URL от ngrok (например: https://abc123.ngrok-free.app)
     * @return int
     */
    public function actionSetupWebhook($ngrokUrl = null)
    {
        if (!$ngrokUrl) {
            $this->stderr("❌ Укажите ngrok URL\n", \yii\helpers\Console::FG_RED);
            $this->stdout("Пример: php yii bot/setup-webhook https://abc123.ngrok-free.app\n");
            return ExitCode::UNSPECIFIED_ERROR;
        }

        // Убираем trailing slash если есть
        $ngrokUrl = rtrim($ngrokUrl, '/');
        
        $botToken = Yii::$app->params['telegramBotToken'];
        $webhookUrl = $ngrokUrl . '/webhook/' . $botToken;

        $this->stdout("🔗 Настройка webhook...\n\n", \yii\helpers\Console::FG_BLUE);
        $this->stdout("Ngrok URL: $ngrokUrl\n");
        $this->stdout("Webhook URL: $webhookUrl\n\n");

        try {
            // Обновляем конфигурацию
            $paramsFile = Yii::getAlias('@app/config/params.php');
            $params = require $paramsFile;
            $params['webhookUrl'] = $webhookUrl;
            
            $paramsContent = "<?php\n\nreturn " . var_export($params, true) . ";\n";
            file_put_contents($paramsFile, $paramsContent);
            
            $this->stdout("✅ Конфигурация обновлена\n", \yii\helpers\Console::FG_GREEN);

            // Устанавливаем webhook
            $telegramBot = Yii::$app->telegramBot;
            $telegramBot->webhookUrl = $webhookUrl; // Обновляем URL в компоненте
            
            $result = $telegramBot->setWebhook($webhookUrl);
            
            if ($result) {
                $this->stdout("✅ Webhook установлен успешно!\n", \yii\helpers\Console::FG_GREEN);
                
                // Проверяем статус
                $webhookInfo = $telegramBot->getWebhookInfo();
                if ($webhookInfo) {
                    $this->stdout("\n📋 Информация о webhook:\n", \yii\helpers\Console::FG_CYAN);
                    $this->stdout("URL: " . $webhookInfo['url'] . "\n");
                    $this->stdout("Pending updates: " . $webhookInfo['pending_update_count'] . "\n");
                }
                
                // Тестируем webhook
                $this->stdout("\n🧪 Тестирование webhook...\n", \yii\helpers\Console::FG_YELLOW);
                $testResult = $this->testWebhookEndpoint($webhookUrl);
                
                if ($testResult) {
                    $this->stdout("✅ Webhook работает корректно!\n", \yii\helpers\Console::FG_GREEN);
                } else {
                    $this->stderr("❌ Webhook не отвечает правильно\n", \yii\helpers\Console::FG_RED);
                }
                
            } else {
                $this->stderr("❌ Не удалось установить webhook\n", \yii\helpers\Console::FG_RED);
                return ExitCode::UNSPECIFIED_ERROR;
            }

        } catch (\Exception $e) {
            $this->stderr("❌ Ошибка: " . $e->getMessage() . "\n", \yii\helpers\Console::FG_RED);
            return ExitCode::UNSPECIFIED_ERROR;
        }

        $this->stdout("\n💡 Теперь можете тестировать бота в Telegram!\n", \yii\helpers\Console::FG_BLUE);
        $this->stdout("Для мониторинга используйте: php yii bot/tail-logs\n");

        return ExitCode::OK;
    }

    /**
     * Тестирует webhook endpoint
     * @param string $webhookUrl
     * @return bool
     */
    private function testWebhookEndpoint($webhookUrl)
    {
        try {
            $testUpdate = [
                'update_id' => 12345,
                'message' => [
                    'message_id' => 1,
                    'from' => [
                        'id' => 123456789,
                        'is_bot' => false,
                        'first_name' => 'Test',
                        'username' => 'testuser'
                    ],
                    'chat' => [
                        'id' => 123456789,
                        'type' => 'private'
                    ],
                    'date' => time(),
                    'text' => '/start'
                ]
            ];

            $client = new \GuzzleHttp\Client(['timeout' => 10, 'verify' => false]);
            $response = $client->post($webhookUrl, [
                'json' => $testUpdate,
                'headers' => ['Content-Type' => 'application/json']
            ]);

            return $response->getStatusCode() === 200;
        } catch (\Exception $e) {
            $this->stdout("Ошибка тестирования: " . $e->getMessage() . "\n");
            return false;
        }
    }

    /**
     * Тест локального webhook endpoint
     * @return int
     */
    public function actionTestLocal()
    {
        $this->stdout("🏠 Тестирование локального webhook...\n\n", \yii\helpers\Console::FG_BLUE);
        
        $botToken = Yii::$app->params['telegramBotToken'];
        $localUrl = "http://groq_bot.localhost/webhook/$botToken";
        
        $this->stdout("Тестируем URL: $localUrl\n\n");
        
        // Очищаем логи
        $this->clearBotLog();
        
        $testUpdate = [
            'update_id' => 12345,
            'message' => [
                'message_id' => 1,
                'from' => [
                    'id' => 123456789,
                    'is_bot' => false,
                    'first_name' => 'Test',
                    'username' => 'testuser'
                ],
                'chat' => [
                    'id' => 123456789,
                    'type' => 'private'
                ],
                'date' => time(),
                'text' => 'Тест локального webhook'
            ]
        ];
        
        try {
            $client = new \GuzzleHttp\Client(['timeout' => 10, 'verify' => false]);
            
            $this->stdout("📤 Отправляем тестовый запрос...\n", \yii\helpers\Console::FG_YELLOW);
            
            $response = $client->post($localUrl, [
                'json' => $testUpdate,
                'headers' => ['Content-Type' => 'application/json']
            ]);
            
            $statusCode = $response->getStatusCode();
            $responseBody = $response->getBody()->getContents();
            
            $this->stdout("📥 Ответ получен:\n", \yii\helpers\Console::FG_GREEN);
            $this->stdout("HTTP Status: $statusCode\n");
            $this->stdout("Response: $responseBody\n\n");
            
            if ($statusCode === 200) {
                $this->stdout("✅ Локальный webhook работает!\n", \yii\helpers\Console::FG_GREEN);
                
                $this->stdout("\n📋 Логи обработки:\n", \yii\helpers\Console::FG_CYAN);
                $this->showBotLog(20);
                
                return ExitCode::OK;
            } else {
                $this->stderr("❌ Неожиданный статус: $statusCode\n", \yii\helpers\Console::FG_RED);
                return ExitCode::UNSPECIFIED_ERROR;
            }
            
        } catch (\Exception $e) {
            $this->stderr("❌ Ошибка: " . $e->getMessage() . "\n", \yii\helpers\Console::FG_RED);
            return ExitCode::UNSPECIFIED_ERROR;
        }
    }

    /**
     * Быстрая диагностика проблем бота
     * @return int
     */
    public function actionDiagnose()
    {
        $this->stdout("🔍 Диагностика проблем бота...\n\n", \yii\helpers\Console::FG_BLUE);
        
        $issues = [];
        $warnings = [];
        
        try {
            // 1. Проверка конфигурации
            $this->stdout("1️⃣ Проверка конфигурации...\n", \yii\helpers\Console::FG_YELLOW);
            
            // Telegram Bot Token
            $telegramToken = Yii::$app->params['telegramBotToken'] ?? null;
            if (empty($telegramToken)) {
                $issues[] = "❌ Telegram Bot Token не установлен в config/params.php";
            } else {
                $this->stdout("✅ Telegram Bot Token: установлен\n", \yii\helpers\Console::FG_GREEN);
            }
            
            // Groq API Key
            $groqApi = Yii::$app->groqApi;
            if (empty($groqApi->apiKey)) {
                $issues[] = "❌ Groq API Key не установлен";
            } else {
                $this->stdout("✅ Groq API Key: установлен\n", \yii\helpers\Console::FG_GREEN);
            }
            
            // 2. Проверка подключений
            $this->stdout("\n2️⃣ Проверка подключений...\n", \yii\helpers\Console::FG_YELLOW);
            
            // Telegram API
            $telegramBot = Yii::$app->telegramBot;
            $botInfo = $telegramBot->getMe();
            if ($botInfo) {
                $this->stdout("✅ Telegram API: подключение OK (@" . $botInfo['username'] . ")\n", \yii\helpers\Console::FG_GREEN);
            } else {
                $issues[] = "❌ Telegram API: не удается подключиться";
            }
            
            // Groq API
            $testResponse = $groqApi->generateResponse('Test connection');
            if ($testResponse) {
                $this->stdout("✅ Groq API: подключение OK\n", \yii\helpers\Console::FG_GREEN);
            } else {
                $issues[] = "❌ Groq API: не удается подключиться";
            }
            
            // 3. Проверка базы данных
            $this->stdout("\n3️⃣ Проверка базы данных...\n", \yii\helpers\Console::FG_YELLOW);
            
            try {
                $db = Yii::$app->db;
                $db->open();
                $this->stdout("✅ База данных: подключение OK\n", \yii\helpers\Console::FG_GREEN);
                
                // Проверка таблиц
                $tables = ['users', 'conversations', 'messages', 'user_memory'];
                foreach ($tables as $table) {
                    try {
                        $count = $db->createCommand("SELECT COUNT(*) FROM $table")->queryScalar();
                        $this->stdout("✅ Таблица $table: $count записей\n", \yii\helpers\Console::FG_GREEN);
                    } catch (\Exception $e) {
                        $issues[] = "❌ Таблица $table: " . $e->getMessage();
                    }
                }
            } catch (\Exception $e) {
                $issues[] = "❌ База данных: " . $e->getMessage();
            }
            
            // 4. Проверка webhook
            $this->stdout("\n4️⃣ Проверка webhook...\n", \yii\helpers\Console::FG_YELLOW);
            
            $webhookInfo = $telegramBot->getWebhookInfo();
            if ($webhookInfo) {
                if (!empty($webhookInfo['url'])) {
                    $this->stdout("✅ Webhook URL: " . $webhookInfo['url'] . "\n", \yii\helpers\Console::FG_GREEN);
                    $this->stdout("📊 Pending updates: " . $webhookInfo['pending_update_count'] . "\n");
                    
                    if (isset($webhookInfo['last_error_date'])) {
                        $warnings[] = "⚠️ Последняя ошибка webhook: " . date('Y-m-d H:i:s', $webhookInfo['last_error_date']) . " - " . $webhookInfo['last_error_message'];
                    }
                } else {
                    $warnings[] = "⚠️ Webhook не установлен (используется polling режим)";
                }
            } else {
                $issues[] = "❌ Не удается получить информацию о webhook";
            }
            
            // 5. Проверка файлов и папок
            $this->stdout("\n5️⃣ Проверка файлов и папок...\n", \yii\helpers\Console::FG_YELLOW);
            
            $paths = [
                '@app/logs' => 'Папка логов',
                '@app/logs/bot.log' => 'Лог файл бота',
                '@runtime' => 'Runtime папка',
                '@runtime/logs' => 'Системные логи'
            ];
            
            foreach ($paths as $alias => $description) {
                $path = Yii::getAlias($alias);
                if (file_exists($path)) {
                    if (is_dir($path)) {
                        $this->stdout("✅ $description: папка существует\n", \yii\helpers\Console::FG_GREEN);
                    } else {
                        $size = round(filesize($path) / 1024, 2);
                        $this->stdout("✅ $description: файл существует ({$size} KB)\n", \yii\helpers\Console::FG_GREEN);
                    }
                } else {
                    if ($alias === '@app/logs/bot.log') {
                        $warnings[] = "⚠️ $description: файл не существует (будет создан при первом использовании)";
                    } else {
                        $issues[] = "❌ $description: не существует";
                    }
                }
            }
            
        } catch (\Exception $e) {
            $issues[] = "❌ Критическая ошибка диагностики: " . $e->getMessage();
        }
        
        // Результаты диагностики
        $this->stdout("\n" . str_repeat("=", 50) . "\n", \yii\helpers\Console::FG_CYAN);
        $this->stdout("📋 РЕЗУЛЬТАТЫ ДИАГНОСТИКИ\n", \yii\helpers\Console::FG_CYAN);
        $this->stdout(str_repeat("=", 50) . "\n", \yii\helpers\Console::FG_CYAN);
        
        if (empty($issues) && empty($warnings)) {
            $this->stdout("🎉 Все проверки пройдены успешно! Бот должен работать корректно.\n", \yii\helpers\Console::FG_GREEN);
        } else {
            if (!empty($issues)) {
                $this->stdout("\n🚨 КРИТИЧЕСКИЕ ПРОБЛЕМЫ:\n", \yii\helpers\Console::FG_RED);
                foreach ($issues as $issue) {
                    $this->stdout("  $issue\n", \yii\helpers\Console::FG_RED);
                }
            }
            
            if (!empty($warnings)) {
                $this->stdout("\n⚠️ ПРЕДУПРЕЖДЕНИЯ:\n", \yii\helpers\Console::FG_YELLOW);
                foreach ($warnings as $warning) {
                    $this->stdout("  $warning\n", \yii\helpers\Console::FG_YELLOW);
                }
            }
        }
        
        $this->stdout("\n💡 РЕКОМЕНДАЦИИ:\n", \yii\helpers\Console::FG_BLUE);
        $this->stdout("  • Используйте 'php yii bot/test-message <chat_id>' для тестирования\n");
        $this->stdout("  • Проверьте логи: 'php yii bot/show-logs'\n");
        $this->stdout("  • Мониторинг в реальном времени: 'php yii bot/tail-logs'\n");
        
        return empty($issues) ? ExitCode::OK : ExitCode::UNSPECIFIED_ERROR;
    }

}
 