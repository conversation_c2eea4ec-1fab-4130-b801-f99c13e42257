<?php

namespace app\models;

use Yii;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "user_memory".
 *
 * @property int $id
 * @property int $user_id
 * @property string $category
 * @property string $key_name
 * @property string $value
 * @property int $importance
 * @property int|null $source_message_id
 * @property string $created_at
 * @property string $updated_at
 *
 * @property User $user
 * @property Message $sourceMessage
 */
class UserMemory extends ActiveRecord
{
    // Categories for organizing memory
    const CATEGORY_PERSONAL = 'personal';
    const CATEGORY_PREFERENCES = 'preferences';
    const CATEGORY_FACTS = 'facts';
    const CATEGORY_GOALS = 'goals';
    const CATEGORY_RELATIONSHIPS = 'relationships';
    const CATEGORY_WORK = 'work';
    const CATEGORY_HOBBIES = 'hobbies';
    const CATEGORY_OTHER = 'other';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'user_memory';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['user_id', 'category', 'key_name', 'value'], 'required'],
            [['user_id', 'importance', 'source_message_id'], 'integer'],
            [['value'], 'string'],
            [['created_at', 'updated_at'], 'safe'],
            [['category'], 'string', 'max' => 100],
            [['key_name'], 'string', 'max' => 255],
            [['importance'], 'integer', 'min' => 1, 'max' => 10],
            [['category'], 'in', 'range' => [
                self::CATEGORY_PERSONAL,
                self::CATEGORY_PREFERENCES,
                self::CATEGORY_FACTS,
                self::CATEGORY_GOALS,
                self::CATEGORY_RELATIONSHIPS,
                self::CATEGORY_WORK,
                self::CATEGORY_HOBBIES,
                self::CATEGORY_OTHER,
            ]],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => 'User ID',
            'category' => 'Category',
            'key_name' => 'Key Name',
            'value' => 'Value',
            'importance' => 'Importance',
            'source_message_id' => 'Source Message ID',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for the associated user.
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(User::class, ['id' => 'user_id']);
    }

    /**
     * Gets query for the associated source message.
     *
     * @return \yii\db\ActiveQuery
     */
    public function getSourceMessage()
    {
        return $this->hasOne(Message::class, ['id' => 'source_message_id']);
    }

    /**
     * Saves or updates memory item
     *
     * @param int $userId
     * @param string $category
     * @param string $keyName
     * @param string $value
     * @param int $importance
     * @param int|null $sourceMessageId
     * @return static
     */
    public static function saveMemory($userId, $category, $keyName, $value, $importance = 5, $sourceMessageId = null)
    {
        $memory = static::findOne(['user_id' => $userId, 'key_name' => $keyName]);
        
        if (!$memory) {
            $memory = new static();
            $memory->user_id = $userId;
            $memory->category = $category;
            $memory->key_name = $keyName;
            $memory->created_at = date('Y-m-d H:i:s');
        }
        
        $memory->value = $value;
        $memory->importance = $importance;
        $memory->source_message_id = $sourceMessageId;
        $memory->updated_at = date('Y-m-d H:i:s');
        $memory->save();
        
        return $memory;
    }

    /**
     * Gets user memory by category
     *
     * @param int $userId
     * @param string|null $category
     * @param int $limit
     * @return static[]
     */
    public static function getUserMemory($userId, $category = null, $limit = 50)
    {
        $query = static::find()
            ->where(['user_id' => $userId])
            ->orderBy(['importance' => SORT_DESC, 'updated_at' => SORT_DESC]);
        
        if ($category) {
            $query->andWhere(['category' => $category]);
        }
        
        return $query->limit($limit)->all();
    }

    /**
     * Gets memory for context formation
     *
     * @param int $userId
     * @param int $minImportance
     * @return array
     */
    public static function getMemoryForContext($userId, $minImportance = 3)
    {
        $memories = static::find()
            ->where(['user_id' => $userId])
            ->andWhere(['>=', 'importance', $minImportance])
            ->orderBy(['importance' => SORT_DESC, 'updated_at' => SORT_DESC])
            ->limit(20)
            ->all();
        
        $context = [];
        foreach ($memories as $memory) {
            $context[] = "{$memory->key_name}: {$memory->value}";
        }
        
        return $context;
    }

    /**
     * Searches memory by keyword
     *
     * @param int $userId
     * @param string $keyword
     * @return static[]
     */
    public static function searchMemory($userId, $keyword)
    {
        return static::find()
            ->where(['user_id' => $userId])
            ->andWhere(['or',
                ['like', 'key_name', $keyword],
                ['like', 'value', $keyword]
            ])
            ->orderBy(['importance' => SORT_DESC])
            ->all();
    }

    /**
     * Deletes memory item
     *
     * @param int $userId
     * @param string $keyName
     * @return bool
     */
    public static function deleteMemory($userId, $keyName)
    {
        $memory = static::findOne(['user_id' => $userId, 'key_name' => $keyName]);
        return $memory ? $memory->delete() : false;
    }

    /**
     * Gets memory statistics
     *
     * @param int $userId
     * @return array
     */
    public static function getMemoryStats($userId)
    {
        $total = static::find()->where(['user_id' => $userId])->count();
        $byCategory = static::find()
            ->select(['category', 'COUNT(*) as count'])
            ->where(['user_id' => $userId])
            ->groupBy('category')
            ->asArray()
            ->all();
        
        $categories = [];
        foreach ($byCategory as $item) {
            $categories[$item['category']] = $item['count'];
        }
        
        return [
            'total' => $total,
            'by_category' => $categories,
        ];
    }

    /**
     * Gets available categories
     *
     * @return array
     */
    public static function getCategories()
    {
        return [
            self::CATEGORY_PERSONAL => 'Личная информация',
            self::CATEGORY_PREFERENCES => 'Предпочтения',
            self::CATEGORY_FACTS => 'Факты',
            self::CATEGORY_GOALS => 'Цели',
            self::CATEGORY_RELATIONSHIPS => 'Отношения',
            self::CATEGORY_WORK => 'Работа',
            self::CATEGORY_HOBBIES => 'Хобби',
            self::CATEGORY_OTHER => 'Другое',
        ];
    }
}