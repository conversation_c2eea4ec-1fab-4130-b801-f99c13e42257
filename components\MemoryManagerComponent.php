<?php

namespace app\components;

use Yii;
use yii\base\Component;
use app\models\UserMemory;

/**
 * Memory Manager Component for extracting and managing user memory
 */
class MemoryManagerComponent extends Component
{
    public $extractionPrompt = 'Проанализируй следующее сообщение пользователя и извлеки важную информацию о нем. Верни результат в JSON формате с полями: category (personal/preferences/facts/goals/relationships/work/hobbies/other), key_name (краткое название), value (значение), importance (1-10). Если важной информации нет, верни пустой массив.';
    
    /**
     * Extracts important information from user message
     *
     * @param string $message
     * @param int $userId
     * @param int|null $messageId
     * @return array
     */
    public function extractMemoryFromMessage($message, $userId, $messageId = null)
    {
        $groqApi = Yii::$app->groqApi;
        
        // Prepare extraction prompt
        $extractionMessages = [
            [
                'role' => 'system',
                'content' => $this->extractionPrompt
            ],
            [
                'role' => 'user',
                'content' => $message
            ]
        ];
        
        // Get response from AI
        $response = $groqApi->chatCompletion($extractionMessages, [
            'temperature' => 0.3, // Lower temperature for more consistent extraction
            'max_tokens' => 500,
        ]);
        
        if (!$response) {
            return [];
        }
        
        $responseText = $groqApi->extractResponseText($response);
        
        // Try to parse JSON response
        $extractedData = $this->parseExtractionResponse($responseText);
        
        // Save extracted memories
        $savedMemories = [];
        foreach ($extractedData as $item) {
            if ($this->isValidMemoryItem($item)) {
                $memory = UserMemory::saveMemory(
                    $userId,
                    $item['category'],
                    $item['key_name'],
                    $item['value'],
                    $item['importance'],
                    $messageId
                );
                $savedMemories[] = $memory;
            }
        }
        
        return $savedMemories;
    }
    
    /**
     * Parses AI response for memory extraction
     *
     * @param string $response
     * @return array
     */
    private function parseExtractionResponse($response)
    {
        // Clean response - remove markdown formatting
        $response = preg_replace('/```json\s*/', '', $response);
        $response = preg_replace('/```\s*$/', '', $response);
        $response = trim($response);
        
        // Try to decode JSON
        $data = json_decode($response, true);
        
        if (json_last_error() === JSON_ERROR_NONE) {
            // If single object, wrap in array
            if (isset($data['category'])) {
                return [$data];
            }
            return is_array($data) ? $data : [];
        }
        
        // If JSON parsing failed, try to extract manually
        return $this->manualExtraction($response);
    }
    
    /**
     * Manual extraction as fallback
     *
     * @param string $text
     * @return array
     */
    private function manualExtraction($text)
    {
        // Simple pattern matching for common personal information
        $memories = [];
        
        // Name extraction
        if (preg_match('/меня зовут\s+([а-яё\w\s]+)/iu', $text, $matches) ||
            preg_match('/я\s+([а-яё\w]+)/iu', $text, $matches)) {
            $memories[] = [
                'category' => UserMemory::CATEGORY_PERSONAL,
                'key_name' => 'имя',
                'value' => trim($matches[1]),
                'importance' => 8
            ];
        }
        
        // Age extraction
        if (preg_match('/мне\s+(\d+)\s+лет/iu', $text, $matches)) {
            $memories[] = [
                'category' => UserMemory::CATEGORY_PERSONAL,
                'key_name' => 'возраст',
                'value' => $matches[1] . ' лет',
                'importance' => 7
            ];
        }
        
        // City extraction
        if (preg_match('/живу в\s+([а-яё\w\s]+)/iu', $text, $matches) ||
            preg_match('/из города\s+([а-яё\w\s]+)/iu', $text, $matches)) {
            $memories[] = [
                'category' => UserMemory::CATEGORY_PERSONAL,
                'key_name' => 'город',
                'value' => trim($matches[1]),
                'importance' => 6
            ];
        }
        
        // Work extraction
        if (preg_match('/работаю\s+([а-яё\w\s]+)/iu', $text, $matches) ||
            preg_match('/профессия\s+([а-яё\w\s]+)/iu', $text, $matches)) {
            $memories[] = [
                'category' => UserMemory::CATEGORY_WORK,
                'key_name' => 'профессия',
                'value' => trim($matches[1]),
                'importance' => 7
            ];
        }
        
        return $memories;
    }
    
    /**
     * Validates memory item
     *
     * @param array $item
     * @return bool
     */
    private function isValidMemoryItem($item)
    {
        return isset($item['category'], $item['key_name'], $item['value']) &&
               !empty($item['key_name']) &&
               !empty($item['value']) &&
               in_array($item['category'], array_keys(UserMemory::getCategories()));
    }
    
    /**
     * Gets user memory for context
     *
     * @param int $userId
     * @return string
     */
    public function getMemoryContext($userId)
    {
        $memories = UserMemory::getMemoryForContext($userId);
        
        if (empty($memories)) {
            return '';
        }
        
        $context = "Важная информация о пользователе:\n";
        $context .= implode("\n", $memories);
        
        return $context;
    }
    
    /**
     * Formats memory for display
     *
     * @param int $userId
     * @param string|null $category
     * @return array
     */
    public function formatMemoryForDisplay($userId, $category = null)
    {
        $memories = UserMemory::getUserMemory($userId, $category);
        $categories = UserMemory::getCategories();
        
        $formatted = [];
        foreach ($memories as $memory) {
            $categoryName = $categories[$memory->category] ?? $memory->category;
            
            if (!isset($formatted[$categoryName])) {
                $formatted[$categoryName] = [];
            }
            
            $formatted[$categoryName][] = [
                'key' => $memory->key_name,
                'value' => $memory->value,
                'importance' => $memory->importance,
                'updated' => $memory->updated_at,
            ];
        }
        
        return $formatted;
    }
    
    /**
     * Suggests memory updates based on conversation
     *
     * @param string $message
     * @param int $userId
     * @return array
     */
    public function suggestMemoryUpdates($message, $userId)
    {
        $existingMemory = UserMemory::getUserMemory($userId);
        
        if (empty($existingMemory)) {
            return [];
        }
        
        $groqApi = Yii::$app->groqApi;
        
        // Create context with existing memory
        $memoryContext = "Существующая информация о пользователе:\n";
        foreach ($existingMemory as $memory) {
            $memoryContext .= "- {$memory->key_name}: {$memory->value}\n";
        }
        
        $suggestionMessages = [
            [
                'role' => 'system',
                'content' => 'Проанализируй новое сообщение пользователя и существующую информацию о нем. Предложи обновления или дополнения к сохраненной информации. Верни JSON с полями: action (update/add), key_name, new_value, reason.'
            ],
            [
                'role' => 'user',
                'content' => $memoryContext . "\nНовое сообщение: " . $message
            ]
        ];
        
        $response = $groqApi->chatCompletion($suggestionMessages, [
            'temperature' => 0.3,
            'max_tokens' => 300,
        ]);
        
        if (!$response) {
            return [];
        }
        
        $responseText = $groqApi->extractResponseText($response);
        return $this->parseExtractionResponse($responseText);
    }
}